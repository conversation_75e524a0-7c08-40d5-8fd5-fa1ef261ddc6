# DeFi Agent Server Environment Variables

# Upstash Configuration (for storage)
# URL should start with https:// (e.g., https://your-redis-instance.upstash.io)
UPSTASH_URL=https://your-redis-instance.upstash.io
UPSTASH_TOKEN=your_upstash_token_here

# MCP Server Configuration

# Santiment API Key for crypto sentiment analysis
# Get your API key from: https://app.santiment.net/
SANTIMENT_API_KEY=your_santiment_api_key_here

# The Graph API Key for Uniswap data (optional - for higher rate limits)
# Get your API key from: https://thegraph.com/
THE_GRAPH_API_KEY=your_graph_api_key_here

# Exchange configuration for crypto indicators
# Default: binance (no API key required for public data)
EXCHANGE_NAME=binance

# Optional: Add other API keys as needed
# BINANCE_API_KEY=your_binance_api_key_here
# BINANCE_SECRET_KEY=your_binance_secret_key_here
