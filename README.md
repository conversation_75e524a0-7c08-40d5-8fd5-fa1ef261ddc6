# DeFi Agent Server

A Mastra-powered DeFi agent with integrated MCP (Model Context Protocol) servers for cryptocurrency analysis and trading insights.

## Features

- **Weather Agent**: Basic weather information (example)
- **Crypto Sentiment Analysis**: Market sentiment using Santiment API
- **Uniswap Token Analysis**: Comprehensive token rankings, metrics, and comparisons
- **Fear and Greed Index**: Cryptocurrency market sentiment indicator
- **Technical Indicators**: Price data and technical analysis from Binance
- **MCP Integration**: Modular tools via Model Context Protocol

## Installation

Install dependencies:

```bash
bun install
```

## Configuration

1. Copy the environment template:
```bash
cp .env.example .env
```

2. Configure your API keys in `.env`:
```bash
# Required for sentiment analysis
SANTIMENT_API_KEY=your_santiment_api_key_here

# Optional for Uniswap analysis (higher rate limits)
THE_GRAPH_API_KEY=your_graph_api_key_here

# Storage configuration
UPSTASH_URL=your_upstash_url_here
UPSTASH_TOKEN=your_upstash_token_here
```

## Running the Server

Development mode:
```bash
bun run dev
```

Production build:
```bash
bun run build
```

Direct execution:
```bash
bun run index.ts
```

## Usage

### Crypto Sentiment Analysis

The crypto sentiment tools can be used directly or through the specialized Crypto Sentiment Agent:

```typescript
import { cryptoSentimentAgent } from './src/mastra/agents/crypto-sentiment-agent';

// Natural language queries
const response = await cryptoSentimentAgent.generate(
  "What's the current sentiment for Bitcoin and are there any social volume alerts?"
);
```

**Example queries:**
- "What's the sentiment for Bitcoin this week?"
- "Show me social volume trends for Ethereum"
- "Are there any social volume spikes for Solana?"
- "What are the trending words in crypto discussions?"
- "How much of crypto discussion is dominated by Bitcoin?"

### Direct Tool Usage

```typescript
import { cryptoSentimentTools } from './src/mastra/tools/crypto-sentiment';

// Get sentiment balance
const sentiment = await cryptoSentimentTools.getSentimentBalance.execute({
  context: { asset: 'bitcoin', days: 7 }
});

// Check for social volume alerts
const alert = await cryptoSentimentTools.alertSocialShift.execute({
  context: { asset: 'ethereum', threshold: 50 }
});
```

### Uniswap Token Analysis

The Uniswap agent provides comprehensive token analysis and market insights:

```typescript
import { uniswapAgent } from './src/mastra/agents/uniswap-agent';

// Natural language queries
const response = await uniswapAgent.generate(
  "What are the top 20 Uniswap tokens by 24h volume and how have their prices changed?"
);
```

**Example queries:**
- "Show me the top 50 tokens by trading volume"
- "What's the detailed analysis for the UNI token?"
- "Compare WETH, USDC, and WBTC performance"
- "Which tokens have the highest price increases today?"
- "What are the most liquid tokens on Uniswap?"

**Direct tool usage:**
```typescript
import { uniswapTokenTools } from './src/mastra/tools/uniswap-tokens';

// Get top tokens
const topTokens = await uniswapTokenTools.getTopUniswapTokens.execute({
  context: { limit: 20, sortBy: "volumeUSD", timeframe: "24h" }
});

// Get token details
const details = await uniswapTokenTools.getTokenDetails.execute({
  context: { tokenAddress: "******************************************" }
});

// Compare tokens
const comparison = await uniswapTokenTools.compareTokens.execute({
  context: {
    tokenAddresses: ["******************************************", "******************************************"]
  }
});
```

## MCP Servers

This project includes three MCP servers for crypto functionality:

1. **crypto-sentiment-mcp**: Sentiment analysis using Santiment API
2. **crypto-feargreed-mcp**: Fear and greed index data
3. **crypto-indicators-mcp**: Technical indicators using Binance data

See `src/mastra/mcp/README.md` for detailed documentation.

## Project Structure

```
src/mastra/
├── agents/           # Mastra agents
├── tools/           # Custom tools
├── workflows/       # Mastra workflows
├── mcp/            # MCP servers and tools
│   ├── crypto-sentiment-mcp/
│   ├── crypto-feargreed-mcp/
│   └── crypto-indicators-mcp/
└── index.ts        # Main Mastra configuration
```

## Available Tools

### Weather Tools
- Weather information and forecasting

### Crypto Sentiment Analysis Tools
- **Sentiment Balance**: Positive vs negative sentiment analysis for cryptocurrencies
- **Social Volume**: Track social media mentions and community engagement
- **Social Shift Alerts**: Detect significant spikes or drops in social volume
- **Trending Words**: Discover trending topics in crypto discussions
- **Social Dominance**: Measure percentage of crypto discussions focused on specific assets

### Uniswap Token Analysis Tools
- **Top Tokens**: Retrieve and rank top 100 tokens by volume, price changes, and liquidity
- **Token Details**: Comprehensive token metrics including TVL, volume, and price history
- **Token Comparison**: Side-by-side analysis of multiple tokens with performance insights
- **Market Overview**: Real-time market data and trend analysis
- **Pool Analysis**: Liquidity pool information and fee tier analysis

### Additional Crypto Tools (Planned)
- Market fear and greed index
- Price data from exchanges
- Technical indicators (RSI, MACD, etc.)
- Trading volume analysis
- Order book depth data

## Technology Stack

- **Runtime**: [Bun](https://bun.sh) - Fast all-in-one JavaScript runtime
- **Framework**: [Mastra](https://mastra.ai) - AI agent framework
- **Protocol**: [MCP](https://modelcontextprotocol.io) - Model Context Protocol
- **Storage**: Upstash Redis
- **APIs**: Santiment, The Graph (Uniswap V3), Binance, Alternative.me
