# Uniswap Token Analysis Setup Guide

This guide explains how to set up the Uniswap token analysis system for production use with real data.

## 🚀 Quick Start (Demo Mode)

The system works out of the box with mock data for demonstration purposes:

```bash
# Clone and install
git clone <repository>
cd defi-agent-server
bun install

# Run the server
bun run dev

# Test the system
bun run test-all-tools.ts
```

## 🔑 Production Setup with Real Data

To use real Uniswap V3 data, you need a The Graph API key:

### Step 1: Get The Graph API Key

1. Visit [The Graph Studio](https://thegraph.com/studio/)
2. Sign up for an account
3. Create a new API key
4. Copy your API key

### Step 2: Configure Environment

Add your API key to `.env`:

```bash
# Copy the example file
cp .env.example .env

# Edit .env and add your API key
THE_GRAPH_API_KEY=your_actual_api_key_here
```

### Step 3: Verify Real Data

```bash
# Test with real data
bun run test-all-tools.ts
```

You should see real Uniswap token data instead of mock data.

## 📊 Available Tools

### 1. Get Top Tokens
```typescript
const topTokens = await uniswapTokenTools.getTopUniswapTokens.execute({
  context: {
    limit: 50,                    // Number of tokens (1-100)
    sortBy: "volumeUSD",         // Ranking criteria
    timeframe: "24h",            // Analysis timeframe
    includeHistorical: true,     // Include price changes
    minVolume: 10000,           // Minimum volume filter
  }
});
```

**Ranking Options:**
- `volumeUSD` - 24h trading volume
- `totalValueLockedUSD` - Total value locked
- `priceChange24h` - 24h price change
- `priceChange7d` - 7d price change

### 2. Get Token Details
```typescript
const details = await uniswapTokenTools.getTokenDetails.execute({
  context: {
    tokenAddress: "******************************************", // UNI
    includeHistorical: true,     // Price change data
    includePools: true,          // Pool information
    historicalDays: 30,         // Days of history
  }
});
```

### 3. Compare Tokens
```typescript
const comparison = await uniswapTokenTools.compareTokens.execute({
  context: {
    tokenAddresses: [
      "******************************************", // WETH
      "******************************************", // UNI
    ],
    includeHistorical: true,
    comparisonMetrics: ["volume", "priceChange", "tvl"],
  }
});
```

## 🤖 Using the Agent

The Uniswap agent provides natural language access to all tools:

```typescript
import { uniswapAgent } from './src/mastra/agents/uniswap-agent';

const response = await uniswapAgent.generate(
  "What are the top 10 tokens by volume and how have they performed this week?"
);
```

**Example Queries:**
- "Show me the top 20 tokens by trading volume"
- "What's the detailed analysis for the UNI token?"
- "Compare WETH, USDC, and WBTC performance"
- "Which tokens have the highest price increases today?"
- "What are the most liquid tokens on Uniswap?"

## 🏗️ Architecture

```
src/mastra/
├── agents/
│   └── uniswap-agent.ts          # Natural language interface
├── tools/
│   └── uniswap-tokens/
│       ├── index.ts               # Main exports
│       ├── types.ts               # TypeScript interfaces
│       ├── uniswap-client.ts      # GraphQL client
│       ├── utils.ts               # Helper functions
│       ├── get-top-tokens.ts      # Top tokens tool
│       ├── get-token-details.ts   # Token details tool
│       ├── compare-tokens.ts      # Token comparison tool
│       └── README.md              # Detailed documentation
└── index.ts                      # Mastra configuration
```

## 📈 Data Sources

- **Primary**: Uniswap V3 Subgraph via The Graph Network
- **Endpoint**: `https://gateway.thegraph.com/api/{API_KEY}/subgraphs/id/5zvR82QoaXYFyDEKLZ9t6v9adgnptxYpKpSbxtgVENFV`
- **Fallback**: Mock data for demonstration (when no API key)
- **Update Frequency**: Near real-time (< 1 minute delay)

## 🔧 Configuration Options

### Rate Limits
- **With API Key**: 1000 requests/minute
- **Without API Key**: Demo mode with mock data

### Caching
- **Default TTL**: 5 minutes for rankings, 3 minutes for details
- **Storage**: In-memory (Redis recommended for production)

### Error Handling
- Comprehensive error messages
- Graceful fallbacks
- Input validation with Zod schemas

## 🚨 Important Notes

### Mock Data vs Real Data
- **Mock Data**: Used when `THE_GRAPH_API_KEY` is not set
- **Real Data**: Used when valid API key is provided
- **Indicators**: Console messages show which mode is active

### API Key Requirements
- The Graph has deprecated free public endpoints
- Production use requires a paid API key
- Free tier available with rate limits

### Token Addresses
Common token addresses for testing:
```typescript
const tokens = {
  WETH: '******************************************',
  USDC: '******************************************',
  UNI: '******************************************',
  WBTC: '******************************************',
  AAVE: '******************************************',
};
```

## 🔍 Troubleshooting

### Common Issues

1. **"Using mock data" message**
   - Solution: Add `THE_GRAPH_API_KEY` to `.env`

2. **"GraphQL error" messages**
   - Check API key validity
   - Verify network connectivity
   - Check rate limits

3. **"No tokens found" errors**
   - Adjust `minVolume` parameter
   - Check token address format
   - Verify query parameters

### Debug Mode
```bash
# Run debug script
bun run debug-uniswap.ts
```

## 📞 Support

- **Documentation**: See `src/mastra/tools/uniswap-tokens/README.md`
- **Examples**: Check `test-all-tools.ts` and `test-uniswap-agent.ts`
- **The Graph**: [thegraph.com/docs](https://thegraph.com/docs)
- **Uniswap**: [docs.uniswap.org](https://docs.uniswap.org)

## ⚠️ Disclaimer

This tool provides data analysis for informational purposes only. Cryptocurrency investments carry significant risks. Always conduct thorough research and consider professional advice before making investment decisions.
