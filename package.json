{"name": "defi-agent-server", "type": "module", "private": true, "scripts": {"dev": "<PERSON>ra dev", "build": "mastra build"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^22.15.21", "mastra": "^0.10.0", "tsx": "^4.19.4"}, "peerDependencies": {"typescript": "^5.8.3"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@mastra/core": "^0.10.0", "@mastra/libsql": "^0.10.0", "@mastra/loggers": "^0.10.0", "@mastra/mcp": "^0.10.0", "@mastra/upstash": "^0.10.0", "@openrouter/ai-sdk-provider": "^0.5.0", "zod": "^3.25.28"}}