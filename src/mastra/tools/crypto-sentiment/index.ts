/**
 * Cryptocurrency Sentiment Analysis Tools
 *
 * A comprehensive suite of MCP tools for analyzing cryptocurrency sentiment
 * and social media metrics using the Santiment API.
 *
 * These tools provide insights into:
 * - Market sentiment balance (positive vs negative mentions)
 * - Social volume and community engagement
 * - Trending topics and discussion themes
 * - Social dominance and attention metrics
 * - Alert detection for significant changes
 */

// Import all sentiment analysis tools
import { alertSocialShiftTool } from './alert-social-shift';
import { getSentimentBalanceTool } from './get-sentiment-balance';
import { getSocialDominanceTool } from './get-social-dominance';
import { getSocialVolumeTool } from './get-social-volume';
import { getTrendingWordsTool } from './get-trending-words';

// Export all sentiment analysis tools
export { alertSocialShiftTool } from './alert-social-shift';
export { getSentimentBalanceTool } from './get-sentiment-balance';
export { getSocialDominanceTool } from './get-social-dominance';
export { getSocialVolumeTool } from './get-social-volume';
export { getTrendingWordsTool } from './get-trending-words';

// Export utility classes and types
export { SantimentClient } from './santiment-client';
export * from './types';
export * from './utils';

/**
 * Collection of all crypto sentiment tools for easy import
 */
export const cryptoSentimentTools = {
  getSentimentBalance: getSentimentBalanceTool,
  getSocialVolume: getSocialVolumeTool,
  alertSocialShift: alertSocialShiftTool,
  getTrendingWords: getTrendingWordsTool,
  getSocialDominance: getSocialDominanceTool,
} as const;

/**
 * Tool descriptions for documentation and discovery
 */
export const toolDescriptions = {
  getSentimentBalance: "Analyze positive vs negative sentiment for cryptocurrencies",
  getSocialVolume: "Track social media mentions and community engagement",
  alertSocialShift: "Detect significant changes in social volume that may indicate market events",
  getTrendingWords: "Discover trending topics and themes in crypto discussions",
  getSocialDominance: "Measure what percentage of crypto discussions focus on specific assets",
} as const;

/**
 * Environment requirements
 */
export const environmentRequirements = {
  SANTIMENT_API_KEY: "Required - Get your API key from https://app.santiment.net/",
} as const;

/**
 * Supported cryptocurrency assets (common examples)
 * Note: Santiment supports many more assets - these are just examples
 */
export const supportedAssets = [
  'bitcoin',
  'ethereum',
  'cardano',
  'polkadot',
  'chainlink',
  'solana',
  'avalanche',
  'polygon',
  'uniswap',
  'aave',
  'compound',
  'maker',
  'synthetix',
  'yearn-finance',
  'curve-dao-token',
  'sushiswap',
  'pancakeswap',
  'binance-coin',
  'cosmos',
  'terra-luna',
  'algorand',
  'tezos',
  'stellar',
  'monero',
  'litecoin',
  'bitcoin-cash',
  'ethereum-classic',
  'zcash',
  'dash',
  'dogecoin',
  'shiba-inu',
] as const;

/**
 * Usage examples for each tool
 */
export const usageExamples = {
  getSentimentBalance: {
    basic: { asset: "bitcoin", days: 7 },
    advanced: { asset: "ethereum", days: 30, interval: "1d" },
  },
  getSocialVolume: {
    basic: { asset: "bitcoin", days: 7 },
    hourly: { asset: "ethereum", days: 1, interval: "1h" },
  },
  alertSocialShift: {
    basic: { asset: "bitcoin", currentPeriodDays: 1, baselineDays: 14, threshold: 50 },
    sensitive: { asset: "ethereum", currentPeriodDays: 1, baselineDays: 30, threshold: 25 },
  },
  getTrendingWords: {
    basic: { days: 7, size: 10 },
    comprehensive: { days: 30, size: 25 },
  },
  getSocialDominance: {
    basic: { asset: "bitcoin", days: 7 },
    detailed: { asset: "ethereum", days: 30, interval: "1d" },
  },
} as const;
