/**
 * Get sentiment balance (positive minus negative sentiment) for a cryptocurrency
 * 
 * This tool retrieves sentiment balance data from Santiment API, which represents
 * the difference between positive and negative sentiment mentions for a cryptocurrency.
 * Positive values indicate bullish sentiment, negative values indicate bearish sentiment.
 */

import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { SantimentClient } from "./santiment-client";
import { getDateRange, formatSentimentBalance, normalizeCryptoSlug, validateCryptoSlug } from "./utils";
import type { SentimentBalanceData } from "./types";

export const getSentimentBalanceTool = createTool({
  id: "get-sentiment-balance",
  description: "Retrieve sentiment balance (positive minus negative sentiment) for a cryptocurrency over a specified time period. Returns sentiment scores where positive values indicate bullish sentiment and negative values indicate bearish sentiment.",
  inputSchema: z.object({
    asset: z.string().describe("Cryptocurrency slug (e.g., 'bitcoin', 'ethereum') or ticker symbol"),
    days: z.number().min(1).max(365).default(7).describe("Number of days to analyze (default: 7, max: 365)"),
    interval: z.enum(["1h", "6h", "12h", "1d", "7d"]).default("1d").describe("Data interval (default: 1d)"),
  }),
  outputSchema: z.object({
    asset: z.string(),
    sentimentBalance: z.number(),
    interpretation: z.string(),
    period: z.string(),
    formattedData: z.string(),
  }),
  execute: async ({ context }) => {
    try {
      // Validate and normalize the asset slug
      const normalizedSlug = normalizeCryptoSlug(context.asset);
      if (!validateCryptoSlug(normalizedSlug)) {
        throw new Error(`Invalid cryptocurrency slug: ${context.asset}. Please use lowercase alphanumeric characters and hyphens only.`);
      }

      // Initialize Santiment client
      const client = new SantimentClient();
      
      // Get date range
      const { from, to } = getDateRange(context.days);
      
      // Fetch sentiment balance data
      const response = await client.getSentimentBalance({
        slug: normalizedSlug,
        from,
        to,
        interval: context.interval,
      });

      const data = response.data as SentimentBalanceData;
      
      if (!data.sentimentBalance || data.sentimentBalance.length === 0) {
        throw new Error(`No sentiment balance data found for ${context.asset}. Please check the asset name and try again.`);
      }

      // Get the latest sentiment balance
      const latest = data.sentimentBalance[data.sentimentBalance.length - 1];
      const sentimentBalance = latest.sentimentBalanceTotal;

      // Generate interpretation
      let interpretation = '';
      if (sentimentBalance > 0.1) {
        interpretation = 'Positive sentiment (bullish) - More positive than negative mentions';
      } else if (sentimentBalance < -0.1) {
        interpretation = 'Negative sentiment (bearish) - More negative than positive mentions';
      } else {
        interpretation = 'Neutral sentiment - Balanced positive and negative mentions';
      }

      // Format the data for human-readable output
      const formattedData = formatSentimentBalance(data.sentimentBalance);

      return {
        asset: normalizedSlug,
        sentimentBalance: Number(sentimentBalance.toFixed(4)),
        interpretation,
        period: `${context.days} days (${context.interval} intervals)`,
        formattedData,
      };

    } catch (error) {
      throw new Error(`Failed to retrieve sentiment balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Usage Examples:
 * 
 * 1. Get Bitcoin sentiment balance for the last 7 days:
 *    { asset: "bitcoin", days: 7 }
 * 
 * 2. Get Ethereum sentiment balance for the last 30 days with daily intervals:
 *    { asset: "ethereum", days: 30, interval: "1d" }
 * 
 * 3. Get sentiment balance using ticker symbol:
 *    { asset: "btc", days: 14 }
 * 
 * The tool returns:
 * - asset: The normalized cryptocurrency slug
 * - sentimentBalance: Numerical sentiment score (-1 to 1 range typically)
 * - interpretation: Human-readable explanation of the sentiment
 * - period: Description of the analyzed time period
 * - formattedData: Detailed formatted data showing recent trends
 */
