/**
 * Get total social media mentions for a cryptocurrency over a specified period
 * 
 * This tool retrieves social volume data from Santiment API, which represents
 * the total number of mentions across various social media platforms including
 * Twitter, Reddit, Telegram, and other crypto-related discussion channels.
 */

import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { SantimentClient } from "./santiment-client";
import { getDateRange, formatSocialVolume, normalizeCryptoSlug, validateCryptoSlug } from "./utils";
import type { SocialVolumeData } from "./types";

export const getSocialVolumeTool = createTool({
  id: "get-social-volume",
  description: "Get total social media mentions for a cryptocurrency over a specified time period. Returns the number of mentions across Twitter, Reddit, Telegram, and other crypto discussion platforms.",
  inputSchema: z.object({
    asset: z.string().describe("Cryptocurrency slug (e.g., 'bitcoin', 'ethereum') or ticker symbol"),
    days: z.number().min(1).max(365).default(7).describe("Number of days to analyze (default: 7, max: 365)"),
    interval: z.enum(["1h", "6h", "12h", "1d", "7d"]).default("1d").describe("Data interval (default: 1d)"),
  }),
  outputSchema: z.object({
    asset: z.string(),
    latestVolume: z.number(),
    totalVolume: z.number(),
    averageVolume: z.number(),
    period: z.string(),
    formattedData: z.string(),
  }),
  execute: async ({ context }) => {
    try {
      // Validate and normalize the asset slug
      const normalizedSlug = normalizeCryptoSlug(context.asset);
      if (!validateCryptoSlug(normalizedSlug)) {
        throw new Error(`Invalid cryptocurrency slug: ${context.asset}. Please use lowercase alphanumeric characters and hyphens only.`);
      }

      // Initialize Santiment client
      const client = new SantimentClient();
      
      // Get date range
      const { from, to } = getDateRange(context.days);
      
      // Fetch social volume data
      const response = await client.getSocialVolume({
        slug: normalizedSlug,
        from,
        to,
        interval: context.interval,
      });

      const data = response.data as SocialVolumeData;
      
      if (!data.socialVolume || data.socialVolume.length === 0) {
        throw new Error(`No social volume data found for ${context.asset}. Please check the asset name and try again.`);
      }

      // Calculate metrics
      const latest = data.socialVolume[data.socialVolume.length - 1];
      const latestVolume = latest.mentionsCount;
      const totalVolume = data.socialVolume.reduce((sum, d) => sum + d.mentionsCount, 0);
      const averageVolume = Math.round(totalVolume / data.socialVolume.length);

      // Format the data for human-readable output
      const formattedData = formatSocialVolume(data.socialVolume);

      return {
        asset: normalizedSlug,
        latestVolume,
        totalVolume,
        averageVolume,
        period: `${context.days} days (${context.interval} intervals)`,
        formattedData,
      };

    } catch (error) {
      throw new Error(`Failed to retrieve social volume: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Usage Examples:
 * 
 * 1. Get Bitcoin social volume for the last 7 days:
 *    { asset: "bitcoin", days: 7 }
 * 
 * 2. Get Ethereum social volume for the last 30 days with daily intervals:
 *    { asset: "ethereum", days: 30, interval: "1d" }
 * 
 * 3. Get hourly social volume data for the last 24 hours:
 *    { asset: "bitcoin", days: 1, interval: "1h" }
 * 
 * 4. Get social volume using ticker symbol:
 *    { asset: "eth", days: 14 }
 * 
 * The tool returns:
 * - asset: The normalized cryptocurrency slug
 * - latestVolume: Most recent mentions count
 * - totalVolume: Total mentions over the entire period
 * - averageVolume: Average mentions per interval
 * - period: Description of the analyzed time period
 * - formattedData: Detailed formatted data showing recent trends and statistics
 * 
 * Social volume is a key indicator of:
 * - Market attention and interest
 * - Potential price movement correlation
 * - Community engagement levels
 * - News and event impact on discussion
 */
