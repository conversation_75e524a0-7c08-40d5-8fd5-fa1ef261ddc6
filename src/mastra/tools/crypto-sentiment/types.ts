/**
 * TypeScript interfaces for Santiment API responses and tool parameters
 */

export interface SantimentApiResponse<T = any> {
  data: T;
  errors?: Array<{
    message: string;
    locations?: Array<{
      line: number;
      column: number;
    }>;
    path?: string[];
  }>;
}

export interface SentimentBalanceData {
  sentimentBalance: Array<{
    datetime: string;
    sentimentBalanceTotal: number;
    sentimentBalanceTwitter: number;
    sentimentBalanceReddit: number;
    sentimentBalanceTelegram: number;
  }>;
}

export interface SocialVolumeData {
  socialVolume: Array<{
    datetime: string;
    mentionsCount: number;
  }>;
}

export interface TrendingWordsData {
  getTrendingWords: Array<{
    datetime: string;
    topWords: Array<{
      word: string;
      score: number;
    }>;
  }>;
}

export interface SocialDominanceData {
  socialDominance: Array<{
    datetime: string;
    socialDominance: number;
  }>;
}

export interface AssetInfo {
  slug: string;
  name: string;
  ticker: string;
}

export interface SantimentQueryParams {
  slug: string;
  from: string;
  to: string;
  interval?: string;
}

export interface AlertThresholds {
  volumeThreshold: number;
  timeWindow: string;
  baselineDays: number;
}

export interface SocialShiftAlert {
  isAlert: boolean;
  currentVolume: number;
  baselineVolume: number;
  percentageChange: number;
  alertType: 'spike' | 'drop' | 'normal';
  message: string;
}
