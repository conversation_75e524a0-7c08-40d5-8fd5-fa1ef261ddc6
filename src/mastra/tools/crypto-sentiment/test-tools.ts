/**
 * Test script for cryptocurrency sentiment analysis tools
 * 
 * This script demonstrates how to use each of the crypto sentiment tools
 * and validates that they're working correctly with the Santiment API.
 * 
 * To run this test:
 * 1. Ensure SANTIMENT_API_KEY is set in your environment
 * 2. Run: bun run src/mastra/tools/crypto-sentiment/test-tools.ts
 */

import { cryptoSentimentTools } from './index';

async function testSentimentBalance() {
  console.log('\n🔍 Testing Sentiment Balance Tool...');
  try {
    const result = await cryptoSentimentTools.getSentimentBalance.execute({
      context: { asset: 'bitcoin', days: 7, interval: '1d' }
    });
    console.log('✅ Sentiment Balance Result:', {
      asset: result.asset,
      sentimentBalance: result.sentimentBalance,
      interpretation: result.interpretation,
    });
  } catch (error) {
    console.error('❌ Sentiment Balance Error:', error);
  }
}

async function testSocialVolume() {
  console.log('\n📊 Testing Social Volume Tool...');
  try {
    const result = await cryptoSentimentTools.getSocialVolume.execute({
      context: { asset: 'ethereum', days: 7, interval: '1d' }
    });
    console.log('✅ Social Volume Result:', {
      asset: result.asset,
      latestVolume: result.latestVolume,
      averageVolume: result.averageVolume,
    });
  } catch (error) {
    console.error('❌ Social Volume Error:', error);
  }
}

async function testSocialShift() {
  console.log('\n🚨 Testing Social Shift Alert Tool...');
  try {
    const result = await cryptoSentimentTools.alertSocialShift.execute({
      context: { 
        asset: 'bitcoin', 
        currentPeriodDays: 1, 
        baselineDays: 14, 
        threshold: 50 
      }
    });
    console.log('✅ Social Shift Result:', {
      asset: result.asset,
      isAlert: result.isAlert,
      alertType: result.alertType,
      percentageChange: result.percentageChange,
    });
  } catch (error) {
    console.error('❌ Social Shift Error:', error);
  }
}

async function testTrendingWords() {
  console.log('\n🔥 Testing Trending Words Tool...');
  try {
    const result = await cryptoSentimentTools.getTrendingWords.execute({
      context: { days: 7, size: 10 }
    });
    console.log('✅ Trending Words Result:', {
      wordCount: result.wordCount,
      topWords: result.trendingWords.slice(0, 5).map(w => w.word),
    });
  } catch (error) {
    console.error('❌ Trending Words Error:', error);
  }
}

async function testSocialDominance() {
  console.log('\n📈 Testing Social Dominance Tool...');
  try {
    const result = await cryptoSentimentTools.getSocialDominance.execute({
      context: { asset: 'ethereum', days: 7, interval: '1d' }
    });
    console.log('✅ Social Dominance Result:', {
      asset: result.asset,
      latestDominance: result.latestDominance,
      averageDominance: result.averageDominance,
    });
  } catch (error) {
    console.error('❌ Social Dominance Error:', error);
  }
}

async function runAllTests() {
  console.log('🚀 Starting Crypto Sentiment Tools Test Suite...');
  
  // Check if API key is available
  if (!process.env.SANTIMENT_API_KEY) {
    console.error('❌ SANTIMENT_API_KEY environment variable is required');
    console.log('Please set your Santiment API key in the .env file');
    console.log('Get your API key from: https://app.santiment.net/');
    return;
  }

  console.log('✅ SANTIMENT_API_KEY found');

  // Run all tests
  await testSentimentBalance();
  await testSocialVolume();
  await testSocialShift();
  await testTrendingWords();
  await testSocialDominance();

  console.log('\n🎉 Test suite completed!');
  console.log('\nIf all tests passed, your crypto sentiment tools are ready to use.');
  console.log('You can now use them with the Crypto Sentiment Agent or directly in your code.');
}

// Run tests if this file is executed directly
if (import.meta.main) {
  runAllTests().catch(console.error);
}

export { runAllTests };

/**
 * Example usage in your application:
 * 
 * ```typescript
 * import { cryptoSentimentTools } from './tools/crypto-sentiment';
 * 
 * // Get Bitcoin sentiment
 * const sentiment = await cryptoSentimentTools.getSentimentBalance.execute({
 *   context: { asset: 'bitcoin', days: 7 }
 * });
 * 
 * // Check for social volume spikes
 * const alert = await cryptoSentimentTools.alertSocialShift.execute({
 *   context: { asset: 'ethereum', threshold: 50 }
 * });
 * 
 * // Get trending topics
 * const trending = await cryptoSentimentTools.getTrendingWords.execute({
 *   context: { days: 7, size: 15 }
 * });
 * ```
 */
