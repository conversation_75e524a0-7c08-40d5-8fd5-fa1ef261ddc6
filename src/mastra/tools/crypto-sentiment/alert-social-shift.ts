/**
 * Detect significant spikes or drops in social volume compared to baseline
 * 
 * This tool analyzes current social volume against a baseline period to detect
 * significant changes that might indicate important market events, news, or
 * shifts in community sentiment that could precede price movements.
 */

import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { SantimentClient } from "./santiment-client";
import { getDateRange, analyzeSocialShift, normalizeCryptoSlug, validateCryptoSlug } from "./utils";
import type { SocialVolumeData, AlertThresholds } from "./types";

export const alertSocialShiftTool = createTool({
  id: "alert-social-shift",
  description: "Detect significant spikes or drops in social volume compared to baseline period. Useful for identifying potential market-moving events or shifts in community attention that may precede price movements.",
  inputSchema: z.object({
    asset: z.string().describe("Cryptocurrency slug (e.g., 'bitcoin', 'ethereum') or ticker symbol"),
    currentPeriodDays: z.number().min(1).max(7).default(1).describe("Days for current period analysis (default: 1, max: 7)"),
    baselineDays: z.number().min(7).max(90).default(14).describe("Days for baseline comparison (default: 14, max: 90)"),
    threshold: z.number().min(10).max(500).default(50).describe("Percentage threshold for alert (default: 50%, min: 10%, max: 500%)"),
    interval: z.enum(["1h", "6h", "12h", "1d"]).default("1d").describe("Data interval (default: 1d)"),
  }),
  outputSchema: z.object({
    asset: z.string(),
    isAlert: z.boolean(),
    alertType: z.enum(["spike", "drop", "normal"]),
    currentVolume: z.number(),
    baselineVolume: z.number(),
    percentageChange: z.number(),
    threshold: z.number(),
    message: z.string(),
    analysis: z.string(),
  }),
  execute: async ({ context }) => {
    try {
      // Validate and normalize the asset slug
      const normalizedSlug = normalizeCryptoSlug(context.asset);
      if (!validateCryptoSlug(normalizedSlug)) {
        throw new Error(`Invalid cryptocurrency slug: ${context.asset}. Please use lowercase alphanumeric characters and hyphens only.`);
      }

      // Initialize Santiment client
      const client = new SantimentClient();
      
      // Get current period data
      const currentRange = getDateRange(context.currentPeriodDays);
      const currentResponse = await client.getSocialVolume({
        slug: normalizedSlug,
        from: currentRange.from,
        to: currentRange.to,
        interval: context.interval,
      });

      const currentData = currentResponse.data as SocialVolumeData;
      
      if (!currentData.socialVolume || currentData.socialVolume.length === 0) {
        throw new Error(`No current social volume data found for ${context.asset}.`);
      }

      // Get baseline period data (excluding current period)
      const baselineEndDate = new Date();
      baselineEndDate.setDate(baselineEndDate.getDate() - context.currentPeriodDays);
      const baselineStartDate = new Date(baselineEndDate);
      baselineStartDate.setDate(baselineStartDate.getDate() - context.baselineDays);

      const baselineResponse = await client.getSocialVolume({
        slug: normalizedSlug,
        from: baselineStartDate.toISOString(),
        to: baselineEndDate.toISOString(),
        interval: context.interval,
      });

      const baselineData = baselineResponse.data as SocialVolumeData;
      
      if (!baselineData.socialVolume || baselineData.socialVolume.length === 0) {
        throw new Error(`No baseline social volume data found for ${context.asset}.`);
      }

      // Calculate averages
      const currentVolume = currentData.socialVolume.reduce((sum, d) => sum + d.mentionsCount, 0) / currentData.socialVolume.length;
      const baselineVolume = baselineData.socialVolume.reduce((sum, d) => sum + d.mentionsCount, 0) / baselineData.socialVolume.length;

      // Set up alert thresholds
      const alertThresholds: AlertThresholds = {
        volumeThreshold: context.threshold,
        timeWindow: `${context.currentPeriodDays}d`,
        baselineDays: context.baselineDays,
      };

      // Analyze for social shift
      const shiftAnalysis = analyzeSocialShift(currentVolume, baselineVolume, alertThresholds);

      // Generate detailed analysis
      let analysis = `📊 Social Volume Analysis for ${normalizedSlug.toUpperCase()}:\n\n`;
      analysis += `Current Period (${context.currentPeriodDays} day${context.currentPeriodDays > 1 ? 's' : ''}): ${Math.round(currentVolume).toLocaleString()} avg mentions\n`;
      analysis += `Baseline Period (${context.baselineDays} days): ${Math.round(baselineVolume).toLocaleString()} avg mentions\n`;
      analysis += `Change: ${shiftAnalysis.percentageChange.toFixed(1)}%\n`;
      analysis += `Alert Threshold: ${context.threshold}%\n\n`;

      if (shiftAnalysis.isAlert) {
        analysis += `🚨 ALERT TRIGGERED!\n`;
        if (shiftAnalysis.alertType === 'spike') {
          analysis += `This significant spike in social volume may indicate:\n`;
          analysis += `• Breaking news or major announcements\n`;
          analysis += `• Increased market interest or FOMO\n`;
          analysis += `• Potential price movement incoming\n`;
          analysis += `• Community excitement or controversy\n`;
        } else {
          analysis += `This significant drop in social volume may indicate:\n`;
          analysis += `• Decreased market interest\n`;
          analysis += `• End of a hype cycle\n`;
          analysis += `• Shift of attention to other assets\n`;
          analysis += `• Potential consolidation period\n`;
        }
      } else {
        analysis += `✅ No significant social volume shift detected.\n`;
        analysis += `Social volume is within normal ranges compared to the baseline period.`;
      }

      return {
        asset: normalizedSlug,
        isAlert: shiftAnalysis.isAlert,
        alertType: shiftAnalysis.alertType,
        currentVolume: Math.round(currentVolume),
        baselineVolume: Math.round(baselineVolume),
        percentageChange: Number(shiftAnalysis.percentageChange.toFixed(1)),
        threshold: context.threshold,
        message: shiftAnalysis.message,
        analysis,
      };

    } catch (error) {
      throw new Error(`Failed to analyze social shift: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Usage Examples:
 * 
 * 1. Check for Bitcoin social volume spikes in the last day vs 2-week baseline:
 *    { asset: "bitcoin", currentPeriodDays: 1, baselineDays: 14, threshold: 50 }
 * 
 * 2. Detect Ethereum social volume changes with higher sensitivity:
 *    { asset: "ethereum", currentPeriodDays: 1, baselineDays: 30, threshold: 25 }
 * 
 * 3. Monitor for significant drops in social attention:
 *    { asset: "cardano", currentPeriodDays: 2, baselineDays: 21, threshold: 40 }
 * 
 * 4. Hourly monitoring for rapid changes:
 *    { asset: "solana", currentPeriodDays: 1, baselineDays: 7, threshold: 75, interval: "1h" }
 * 
 * The tool returns:
 * - isAlert: Whether a significant shift was detected
 * - alertType: Type of shift (spike, drop, or normal)
 * - currentVolume/baselineVolume: Average volumes for comparison
 * - percentageChange: Percentage change from baseline
 * - message: Quick alert message
 * - analysis: Detailed analysis with potential implications
 * 
 * Use cases:
 * - Early detection of market-moving events
 * - Monitoring community sentiment shifts
 * - Identifying potential trading opportunities
 * - Risk management and position sizing
 */
