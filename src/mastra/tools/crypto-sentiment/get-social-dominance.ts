/**
 * Get the percentage of social media discussion dominated by a specific cryptocurrency
 * 
 * This tool retrieves social dominance data from Santiment API, which represents
 * what percentage of the total cryptocurrency discussion is focused on a specific asset.
 * Higher dominance indicates the asset is a major topic of conversation in the crypto community.
 */

import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { SantimentClient } from "./santiment-client";
import { getDateRange, formatSocialDominance, normalizeCryptoSlug, validateCryptoSlug } from "./utils";
import type { SocialDominanceData } from "./types";

export const getSocialDominanceTool = createTool({
  id: "get-social-dominance",
  description: "Get the percentage of social media discussion dominated by a specific cryptocurrency. Shows what portion of total crypto discussions focuses on this asset, indicating its relative importance in community attention.",
  inputSchema: z.object({
    asset: z.string().describe("Cryptocurrency slug (e.g., 'bitcoin', 'ethereum') or ticker symbol"),
    days: z.number().min(1).max(365).default(7).describe("Number of days to analyze (default: 7, max: 365)"),
    interval: z.enum(["1h", "6h", "12h", "1d", "7d"]).default("1d").describe("Data interval (default: 1d)"),
  }),
  outputSchema: z.object({
    asset: z.string(),
    latestDominance: z.number(),
    averageDominance: z.number(),
    maxDominance: z.number(),
    minDominance: z.number(),
    period: z.string(),
    interpretation: z.string(),
    formattedData: z.string(),
    marketContext: z.string(),
  }),
  execute: async ({ context }) => {
    try {
      // Validate and normalize the asset slug
      const normalizedSlug = normalizeCryptoSlug(context.asset);
      if (!validateCryptoSlug(normalizedSlug)) {
        throw new Error(`Invalid cryptocurrency slug: ${context.asset}. Please use lowercase alphanumeric characters and hyphens only.`);
      }

      // Initialize Santiment client
      const client = new SantimentClient();
      
      // Get date range
      const { from, to } = getDateRange(context.days);
      
      // Fetch social dominance data
      const response = await client.getSocialDominance({
        slug: normalizedSlug,
        from,
        to,
        interval: context.interval,
      });

      const data = response.data as SocialDominanceData;
      
      if (!data.socialDominance || data.socialDominance.length === 0) {
        throw new Error(`No social dominance data found for ${context.asset}. Please check the asset name and try again.`);
      }

      // Calculate metrics (convert to percentages)
      const dominanceValues = data.socialDominance.map(d => d.socialDominance * 100);
      const latest = dominanceValues[dominanceValues.length - 1];
      const average = dominanceValues.reduce((sum, val) => sum + val, 0) / dominanceValues.length;
      const max = Math.max(...dominanceValues);
      const min = Math.min(...dominanceValues);

      // Generate interpretation
      const interpretation = generateDominanceInterpretation(latest, normalizedSlug);

      // Format the data for human-readable output
      const formattedData = formatSocialDominance(data.socialDominance, normalizedSlug);

      // Generate market context
      const marketContext = generateMarketContext(latest, average, max, min, normalizedSlug);

      return {
        asset: normalizedSlug,
        latestDominance: Number(latest.toFixed(2)),
        averageDominance: Number(average.toFixed(2)),
        maxDominance: Number(max.toFixed(2)),
        minDominance: Number(min.toFixed(2)),
        period: `${context.days} days (${context.interval} intervals)`,
        interpretation,
        formattedData,
        marketContext,
      };

    } catch (error) {
      throw new Error(`Failed to retrieve social dominance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Generate interpretation based on dominance percentage
 */
function generateDominanceInterpretation(dominance: number, asset: string): string {
  const assetName = asset.charAt(0).toUpperCase() + asset.slice(1);
  
  if (dominance > 15) {
    return `🔥 ${assetName} has extremely high social dominance (${dominance.toFixed(2)}%) - dominating crypto discussions. This level typically indicates major news, events, or significant market movements.`;
  } else if (dominance > 10) {
    return `📈 ${assetName} has very high social dominance (${dominance.toFixed(2)}%) - a major topic in crypto discussions. Strong community attention and potential market impact.`;
  } else if (dominance > 5) {
    return `📊 ${assetName} has high social dominance (${dominance.toFixed(2)}%) - significant attention in crypto discussions. Above-average community interest.`;
  } else if (dominance > 2) {
    return `📋 ${assetName} has moderate social dominance (${dominance.toFixed(2)}%) - reasonable attention in crypto discussions. Normal community interest level.`;
  } else if (dominance > 0.5) {
    return `📉 ${assetName} has low social dominance (${dominance.toFixed(2)}%) - limited attention in crypto discussions. Below-average community interest.`;
  } else {
    return `🔻 ${assetName} has very low social dominance (${dominance.toFixed(2)}%) - minimal attention in crypto discussions. Very limited community interest.`;
  }
}

/**
 * Generate market context analysis
 */
function generateMarketContext(latest: number, average: number, max: number, min: number, asset: string): string {
  const assetName = asset.charAt(0).toUpperCase() + asset.slice(1);
  let context = `📊 ${assetName} Social Dominance Analysis:\n\n`;
  
  // Current vs average comparison
  const vsAverage = ((latest - average) / average) * 100;
  if (Math.abs(vsAverage) > 20) {
    if (vsAverage > 0) {
      context += `🔺 Current dominance is ${Math.abs(vsAverage).toFixed(1)}% ABOVE the period average - heightened attention\n`;
    } else {
      context += `🔻 Current dominance is ${Math.abs(vsAverage).toFixed(1)}% BELOW the period average - reduced attention\n`;
    }
  } else {
    context += `➡️ Current dominance is near the period average (${vsAverage.toFixed(1)}% difference)\n`;
  }

  // Range analysis
  const range = max - min;
  context += `📈 Dominance Range: ${min.toFixed(2)}% - ${max.toFixed(2)}% (${range.toFixed(2)}% spread)\n`;

  // Volatility assessment
  if (range > 5) {
    context += `⚡ High volatility in social attention - significant fluctuations in discussion levels\n`;
  } else if (range > 2) {
    context += `📊 Moderate volatility in social attention - some fluctuations in discussion levels\n`;
  } else {
    context += `🔒 Low volatility in social attention - stable discussion levels\n`;
  }

  // Market implications
  context += `\n💡 Market Implications:\n`;
  
  if (latest > 10) {
    context += `• High dominance may indicate upcoming price volatility\n`;
    context += `• Strong community engagement and market attention\n`;
    context += `• Potential for significant market movements\n`;
  } else if (latest > 5) {
    context += `• Moderate attention suggests normal market interest\n`;
    context += `• Balanced community engagement\n`;
    context += `• Potential for gradual price movements\n`;
  } else {
    context += `• Low dominance suggests limited immediate market impact\n`;
    context += `• Reduced community focus on this asset\n`;
    context += `• May indicate consolidation or accumulation phase\n`;
  }

  // Comparative context
  context += `\n📋 Comparative Context:\n`;
  if (latest > 15) {
    context += `• Dominance level comparable to major market events\n`;
    context += `• Likely trending across multiple social platforms\n`;
  } else if (latest > 5) {
    context += `• Dominance level typical of established major cryptocurrencies\n`;
    context += `• Regular discussion presence in crypto communities\n`;
  } else {
    context += `• Dominance level typical of smaller or less active projects\n`;
    context += `• Limited presence in general crypto discussions\n`;
  }

  return context;
}

/**
 * Usage Examples:
 * 
 * 1. Get Bitcoin social dominance for the last 7 days:
 *    { asset: "bitcoin", days: 7 }
 * 
 * 2. Get Ethereum social dominance for the last 30 days:
 *    { asset: "ethereum", days: 30, interval: "1d" }
 * 
 * 3. Monitor hourly social dominance changes:
 *    { asset: "solana", days: 3, interval: "1h" }
 * 
 * 4. Get social dominance using ticker symbol:
 *    { asset: "ada", days: 14 }
 * 
 * The tool returns:
 * - asset: The normalized cryptocurrency slug
 * - latestDominance: Most recent dominance percentage
 * - averageDominance: Average dominance over the period
 * - maxDominance/minDominance: Highest and lowest dominance values
 * - interpretation: Human-readable explanation of the dominance level
 * - formattedData: Detailed formatted data showing trends
 * - marketContext: Comprehensive analysis with market implications
 * 
 * Social dominance is useful for:
 * - Measuring relative market attention
 * - Identifying trending cryptocurrencies
 * - Comparing community interest across assets
 * - Predicting potential market movements
 * - Understanding market narrative shifts
 */
