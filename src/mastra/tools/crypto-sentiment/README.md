# Cryptocurrency Sentiment Analysis Tools

A comprehensive suite of MCP (Model Context Protocol) tools for analyzing cryptocurrency sentiment and social media metrics using the Santiment API.

## Overview

These tools provide real-time insights into cryptocurrency market sentiment by analyzing social media discussions, community engagement, and trending topics across platforms like Twitter, Reddit, Telegram, and other crypto-focused channels.

## Tools

### 1. Get Sentiment Balance (`get-sentiment-balance`)

Retrieves sentiment balance (positive minus negative sentiment) for a cryptocurrency.

**Parameters:**
- `asset` (string): Cryptocurrency slug or ticker symbol
- `days` (number, default: 7): Number of days to analyze (1-365)
- `interval` (string, default: "1d"): Data interval ("1h", "6h", "12h", "1d", "7d")

**Returns:**
- Sentiment balance score (-1 to 1 range)
- Human-readable interpretation
- Formatted trend data

**Example:**
```typescript
{ asset: "bitcoin", days: 7, interval: "1d" }
```

### 2. Get Social Volume (`get-social-volume`)

Tracks total social media mentions for a cryptocurrency.

**Parameters:**
- `asset` (string): Cryptocurrency slug or ticker symbol
- `days` (number, default: 7): Number of days to analyze (1-365)
- `interval` (string, default: "1d"): Data interval

**Returns:**
- Latest, total, and average mention counts
- Formatted trend analysis

**Example:**
```typescript
{ asset: "ethereum", days: 30, interval: "1d" }
```

### 3. Alert Social Shift (`alert-social-shift`)

Detects significant spikes or drops in social volume compared to baseline.

**Parameters:**
- `asset` (string): Cryptocurrency slug or ticker symbol
- `currentPeriodDays` (number, default: 1): Days for current analysis (1-7)
- `baselineDays` (number, default: 14): Days for baseline comparison (7-90)
- `threshold` (number, default: 50): Percentage threshold for alerts (10-500)
- `interval` (string, default: "1d"): Data interval

**Returns:**
- Alert status and type (spike/drop/normal)
- Percentage change from baseline
- Detailed analysis with market implications

**Example:**
```typescript
{ asset: "solana", currentPeriodDays: 1, baselineDays: 14, threshold: 50 }
```

### 4. Get Trending Words (`get-trending-words`)

Retrieves top trending words in cryptocurrency discussions.

**Parameters:**
- `days` (number, default: 7): Number of days to analyze (1-30)
- `size` (number, default: 10): Number of words to return (5-50)

**Returns:**
- Ranked list of trending words with scores
- Categorized insights (bullish/bearish/technical/DeFi/etc.)
- Market sentiment summary

**Example:**
```typescript
{ days: 7, size: 15 }
```

### 5. Get Social Dominance (`get-social-dominance`)

Measures what percentage of crypto discussions focus on a specific asset.

**Parameters:**
- `asset` (string): Cryptocurrency slug or ticker symbol
- `days` (number, default: 7): Number of days to analyze (1-365)
- `interval` (string, default: "1d"): Data interval

**Returns:**
- Latest, average, max, and min dominance percentages
- Market context and implications
- Comparative analysis

**Example:**
```typescript
{ asset: "cardano", days: 14, interval: "1d" }
```

## Setup

### Environment Variables

Add your Santiment API key to your `.env` file:

```bash
SANTIMENT_API_KEY=your_santiment_api_key_here
```

Get your API key from: https://app.santiment.net/

### Installation

The tools are automatically available when you import them:

```typescript
import { cryptoSentimentTools } from './tools/crypto-sentiment';
```

## Supported Assets

The tools support hundreds of cryptocurrencies. Common examples include:

- `bitcoin` (BTC)
- `ethereum` (ETH)
- `cardano` (ADA)
- `polkadot` (DOT)
- `solana` (SOL)
- `chainlink` (LINK)
- `polygon` (MATIC)
- `avalanche` (AVAX)
- `uniswap` (UNI)
- And many more...

You can use either full names (`bitcoin`) or ticker symbols (`btc`) - the tools will automatically normalize them.

## Usage with Mastra Agent

The tools are designed to work with the Crypto Sentiment Agent:

```typescript
import { cryptoSentimentAgent } from './agents/crypto-sentiment-agent';

// The agent can handle natural language queries like:
// "What's the sentiment for Bitcoin this week?"
// "Are there any social volume spikes for Ethereum?"
// "Show me trending crypto topics"
```

## API Rate Limits

- Santiment API has rate limits based on your subscription tier
- Free tier: Limited requests per day
- Paid tiers: Higher rate limits and more data access
- The tools include error handling for rate limit scenarios

## Error Handling

All tools include comprehensive error handling for:
- Invalid cryptocurrency slugs
- API authentication failures
- Network connectivity issues
- Data availability problems
- Rate limit exceeded scenarios

## Data Interpretation

### Sentiment Balance
- **Positive values (>0.1)**: Bullish sentiment, more positive mentions
- **Negative values (<-0.1)**: Bearish sentiment, more negative mentions
- **Near zero (-0.1 to 0.1)**: Neutral sentiment, balanced mentions

### Social Volume
- **High volume**: Increased attention, potential price volatility
- **Volume spikes**: Often precede significant price movements
- **Low volume**: Reduced interest, potential consolidation

### Social Dominance
- **>15%**: Extremely high attention, major market focus
- **10-15%**: Very high attention, significant community interest
- **5-10%**: High attention, above-average discussion
- **2-5%**: Moderate attention, normal levels
- **<2%**: Low attention, limited community focus

## Best Practices

1. **Combine Multiple Metrics**: Use sentiment, volume, and dominance together for comprehensive analysis
2. **Consider Time Frames**: Different intervals reveal different patterns
3. **Monitor Trends**: Look for changes over time, not just absolute values
4. **Context Matters**: Consider market conditions and external events
5. **Set Appropriate Thresholds**: Adjust alert thresholds based on asset volatility
6. **Regular Monitoring**: Set up regular checks for important assets

## Troubleshooting

### Common Issues

1. **"No data found" errors**: Check asset name spelling and data availability
2. **API authentication errors**: Verify SANTIMENT_API_KEY is set correctly
3. **Rate limit errors**: Reduce request frequency or upgrade API plan
4. **Network timeouts**: Check internet connection and API status

### Support

For issues with:
- **Tools**: Check this documentation and error messages
- **Santiment API**: Visit https://docs.santiment.net/
- **Mastra Framework**: Visit https://docs.mastra.ai/

## License

These tools are part of the DeFi Agent Server project and follow the same licensing terms.
