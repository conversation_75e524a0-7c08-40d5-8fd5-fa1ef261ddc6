/**
 * Utility functions for cryptocurrency sentiment analysis tools
 */

import type { SocialShiftAlert, AlertThresholds } from './types';

/**
 * Format a date to ISO string for Santiment API
 */
export function formatDateForApi(date: Date): string {
  return date.toISOString();
}

/**
 * Get date range for the specified number of days
 */
export function getDateRange(days: number): { from: string; to: string } {
  const to = new Date();
  const from = new Date();
  from.setDate(from.getDate() - days);
  
  return {
    from: formatDateForApi(from),
    to: formatDateForApi(to),
  };
}

/**
 * Calculate percentage change between two values
 */
export function calculatePercentageChange(current: number, baseline: number): number {
  if (baseline === 0) return 0;
  return ((current - baseline) / baseline) * 100;
}

/**
 * Analyze social volume for significant shifts
 */
export function analyzeSocialShift(
  currentVolume: number,
  baselineVolume: number,
  thresholds: AlertThresholds
): SocialShiftAlert {
  const percentageChange = calculatePercentageChange(currentVolume, baselineVolume);
  const absChange = Math.abs(percentageChange);
  
  let alertType: 'spike' | 'drop' | 'normal' = 'normal';
  let isAlert = false;
  let message = '';

  if (absChange >= thresholds.volumeThreshold) {
    isAlert = true;
    if (percentageChange > 0) {
      alertType = 'spike';
      message = `🚨 Social volume SPIKE detected! Current volume (${currentVolume.toLocaleString()}) is ${percentageChange.toFixed(1)}% higher than baseline (${baselineVolume.toLocaleString()})`;
    } else {
      alertType = 'drop';
      message = `📉 Social volume DROP detected! Current volume (${currentVolume.toLocaleString()}) is ${Math.abs(percentageChange).toFixed(1)}% lower than baseline (${baselineVolume.toLocaleString()})`;
    }
  } else {
    message = `✅ Social volume is normal. Current: ${currentVolume.toLocaleString()}, Baseline: ${baselineVolume.toLocaleString()} (${percentageChange.toFixed(1)}% change)`;
  }

  return {
    isAlert,
    currentVolume,
    baselineVolume,
    percentageChange,
    alertType,
    message,
  };
}

/**
 * Format sentiment balance data for human-readable output
 */
export function formatSentimentBalance(data: Array<{ datetime: string; sentimentBalanceTotal: number }>): string {
  if (!data || data.length === 0) {
    return 'No sentiment data available for the specified period.';
  }

  const latest = data[data.length - 1];
  const sentiment = latest.sentimentBalanceTotal;
  
  let interpretation = '';
  if (sentiment > 0.1) {
    interpretation = '😊 Positive sentiment (bullish)';
  } else if (sentiment < -0.1) {
    interpretation = '😟 Negative sentiment (bearish)';
  } else {
    interpretation = '😐 Neutral sentiment';
  }

  return `Latest Sentiment Balance: ${sentiment.toFixed(3)} - ${interpretation}\n\nRecent trend (last ${data.length} data points):\n${data.slice(-5).map(d => `${new Date(d.datetime).toLocaleDateString()}: ${d.sentimentBalanceTotal.toFixed(3)}`).join('\n')}`;
}

/**
 * Format social volume data for human-readable output
 */
export function formatSocialVolume(data: Array<{ datetime: string; mentionsCount: number }>): string {
  if (!data || data.length === 0) {
    return 'No social volume data available for the specified period.';
  }

  const latest = data[data.length - 1];
  const total = data.reduce((sum, d) => sum + d.mentionsCount, 0);
  const average = total / data.length;

  return `Latest Social Volume: ${latest.mentionsCount.toLocaleString()} mentions\nAverage over period: ${Math.round(average).toLocaleString()} mentions\nTotal mentions: ${total.toLocaleString()}\n\nRecent trend (last 5 data points):\n${data.slice(-5).map(d => `${new Date(d.datetime).toLocaleDateString()}: ${d.mentionsCount.toLocaleString()} mentions`).join('\n')}`;
}

/**
 * Format trending words data for human-readable output
 */
export function formatTrendingWords(data: Array<{ topWords: Array<{ word: string; score: number }> }>): string {
  if (!data || data.length === 0) {
    return 'No trending words data available for the specified period.';
  }

  const latest = data[data.length - 1];
  if (!latest.topWords || latest.topWords.length === 0) {
    return 'No trending words found for the specified period.';
  }

  return `🔥 Top Trending Words in Crypto Discussions:\n\n${latest.topWords.map((word, index) => `${index + 1}. ${word.word} (score: ${word.score.toFixed(2)})`).join('\n')}`;
}

/**
 * Format social dominance data for human-readable output
 */
export function formatSocialDominance(data: Array<{ datetime: string; socialDominance: number }>, assetName: string): string {
  if (!data || data.length === 0) {
    return `No social dominance data available for ${assetName} in the specified period.`;
  }

  const latest = data[data.length - 1];
  const dominance = latest.socialDominance * 100; // Convert to percentage

  let interpretation = '';
  if (dominance > 10) {
    interpretation = '🔥 Very high dominance - major topic of discussion';
  } else if (dominance > 5) {
    interpretation = '📈 High dominance - significant attention';
  } else if (dominance > 1) {
    interpretation = '📊 Moderate dominance - normal attention';
  } else {
    interpretation = '📉 Low dominance - limited discussion';
  }

  return `${assetName} Social Dominance: ${dominance.toFixed(2)}% - ${interpretation}\n\nRecent trend (last 5 data points):\n${data.slice(-5).map(d => `${new Date(d.datetime).toLocaleDateString()}: ${(d.socialDominance * 100).toFixed(2)}%`).join('\n')}`;
}

/**
 * Validate cryptocurrency slug format
 */
export function validateCryptoSlug(slug: string): boolean {
  // Basic validation for crypto slugs (lowercase, alphanumeric, hyphens)
  return /^[a-z0-9-]+$/.test(slug);
}

/**
 * Normalize cryptocurrency slug (convert to lowercase, handle common variations)
 */
export function normalizeCryptoSlug(input: string): string {
  const normalized = input.toLowerCase().trim();
  
  // Handle common cryptocurrency name variations
  const commonMappings: Record<string, string> = {
    'btc': 'bitcoin',
    'eth': 'ethereum',
    'ada': 'cardano',
    'dot': 'polkadot',
    'bnb': 'binance-coin',
    'sol': 'solana',
    'matic': 'polygon',
    'avax': 'avalanche',
    'link': 'chainlink',
    'uni': 'uniswap',
  };

  return commonMappings[normalized] || normalized;
}
