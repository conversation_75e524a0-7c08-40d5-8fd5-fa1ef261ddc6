/**
 * Retrieve top trending words in cryptocurrency discussions
 * 
 * This tool fetches the most frequently mentioned and trending words
 * in cryptocurrency-related social media discussions, helping identify
 * current market themes, concerns, and topics of interest.
 */

import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { SantimentClient } from "./santiment-client";
import { getDateRange, formatTrendingWords } from "./utils";
import type { TrendingWordsData } from "./types";

export const getTrendingWordsTool = createTool({
  id: "get-trending-words",
  description: "Retrieve top trending words in cryptocurrency discussions across social media platforms. Helps identify current market themes, sentiment drivers, and topics of community interest.",
  inputSchema: z.object({
    days: z.number().min(1).max(30).default(7).describe("Number of days to analyze (default: 7, max: 30)"),
    size: z.number().min(5).max(50).default(10).describe("Number of trending words to return (default: 10, max: 50)"),
  }),
  outputSchema: z.object({
    period: z.string(),
    wordCount: z.number(),
    trendingWords: z.array(z.object({
      word: z.string(),
      score: z.number(),
      rank: z.number(),
    })),
    formattedData: z.string(),
    insights: z.string(),
  }),
  execute: async ({ context }) => {
    try {
      // Initialize Santiment client
      const client = new SantimentClient();
      
      // Get date range
      const { from, to } = getDateRange(context.days);
      
      // Fetch trending words data
      const response = await client.getTrendingWords({
        from,
        to,
        size: context.size,
      });

      const data = response.data as TrendingWordsData;
      
      if (!data.getTrendingWords || data.getTrendingWords.length === 0) {
        throw new Error(`No trending words data found for the specified period.`);
      }

      // Get the latest trending words
      const latest = data.getTrendingWords[data.getTrendingWords.length - 1];
      
      if (!latest.topWords || latest.topWords.length === 0) {
        throw new Error(`No trending words found for the last ${context.days} days.`);
      }

      // Format trending words with rankings
      const trendingWords = latest.topWords.map((word, index) => ({
        word: word.word,
        score: Number(word.score.toFixed(2)),
        rank: index + 1,
      }));

      // Format the data for human-readable output
      const formattedData = formatTrendingWords(data.getTrendingWords);

      // Generate insights based on trending words
      const insights = generateTrendingWordsInsights(trendingWords);

      return {
        period: `${context.days} days`,
        wordCount: trendingWords.length,
        trendingWords,
        formattedData,
        insights,
      };

    } catch (error) {
      throw new Error(`Failed to retrieve trending words: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Generate insights based on trending words analysis
 */
function generateTrendingWordsInsights(words: Array<{ word: string; score: number; rank: number }>): string {
  if (words.length === 0) {
    return 'No trending words available for analysis.';
  }

  let insights = '🔍 Trending Words Analysis:\n\n';

  // Categorize words
  const categories = {
    bullish: ['moon', 'pump', 'bull', 'rally', 'surge', 'breakout', 'ath', 'hodl', 'buy', 'long'],
    bearish: ['dump', 'crash', 'bear', 'dip', 'sell', 'short', 'correction', 'decline', 'drop', 'fall'],
    technical: ['support', 'resistance', 'fibonacci', 'rsi', 'macd', 'volume', 'chart', 'pattern', 'trend'],
    defi: ['defi', 'yield', 'farming', 'staking', 'liquidity', 'swap', 'pool', 'governance', 'dao'],
    nft: ['nft', 'opensea', 'mint', 'collection', 'art', 'metaverse', 'pfp'],
    institutional: ['etf', 'institutional', 'adoption', 'regulation', 'sec', 'compliance'],
    technology: ['blockchain', 'layer2', 'scaling', 'upgrade', 'fork', 'consensus', 'node'],
  };

  const categorizedWords: Record<string, string[]> = {};
  const uncategorized: string[] = [];

  // Categorize trending words
  words.forEach(({ word }) => {
    let categorized = false;
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => word.toLowerCase().includes(keyword) || keyword.includes(word.toLowerCase()))) {
        if (!categorizedWords[category]) {
          categorizedWords[category] = [];
        }
        categorizedWords[category].push(word);
        categorized = true;
        break;
      }
    }
    if (!categorized) {
      uncategorized.push(word);
    }
  });

  // Generate category-based insights
  if (categorizedWords.bullish && categorizedWords.bullish.length > 0) {
    insights += `🚀 Bullish Sentiment: ${categorizedWords.bullish.join(', ')}\n`;
  }
  
  if (categorizedWords.bearish && categorizedWords.bearish.length > 0) {
    insights += `📉 Bearish Sentiment: ${categorizedWords.bearish.join(', ')}\n`;
  }
  
  if (categorizedWords.technical && categorizedWords.technical.length > 0) {
    insights += `📊 Technical Analysis Focus: ${categorizedWords.technical.join(', ')}\n`;
  }
  
  if (categorizedWords.defi && categorizedWords.defi.length > 0) {
    insights += `🏦 DeFi Interest: ${categorizedWords.defi.join(', ')}\n`;
  }
  
  if (categorizedWords.nft && categorizedWords.nft.length > 0) {
    insights += `🎨 NFT Activity: ${categorizedWords.nft.join(', ')}\n`;
  }
  
  if (categorizedWords.institutional && categorizedWords.institutional.length > 0) {
    insights += `🏛️ Institutional Focus: ${categorizedWords.institutional.join(', ')}\n`;
  }
  
  if (categorizedWords.technology && categorizedWords.technology.length > 0) {
    insights += `⚙️ Technology Discussion: ${categorizedWords.technology.join(', ')}\n`;
  }

  if (uncategorized.length > 0) {
    insights += `\n🔤 Other Notable Terms: ${uncategorized.slice(0, 5).join(', ')}`;
    if (uncategorized.length > 5) {
      insights += ` (+${uncategorized.length - 5} more)`;
    }
  }

  // Add market sentiment summary
  const bullishCount = categorizedWords.bullish?.length || 0;
  const bearishCount = categorizedWords.bearish?.length || 0;
  
  insights += '\n\n📈 Market Sentiment Summary:\n';
  if (bullishCount > bearishCount) {
    insights += `Predominantly bullish sentiment detected (${bullishCount} bullish vs ${bearishCount} bearish terms)`;
  } else if (bearishCount > bullishCount) {
    insights += `Predominantly bearish sentiment detected (${bearishCount} bearish vs ${bullishCount} bullish terms)`;
  } else {
    insights += `Mixed sentiment detected (${bullishCount} bullish vs ${bearishCount} bearish terms)`;
  }

  return insights;
}

/**
 * Usage Examples:
 * 
 * 1. Get top 10 trending words for the last week:
 *    { days: 7, size: 10 }
 * 
 * 2. Get top 20 trending words for the last 3 days:
 *    { days: 3, size: 20 }
 * 
 * 3. Get comprehensive trending words analysis for the last month:
 *    { days: 30, size: 25 }
 * 
 * The tool returns:
 * - period: Time period analyzed
 * - wordCount: Number of trending words returned
 * - trendingWords: Array of words with scores and rankings
 * - formattedData: Human-readable formatted output
 * - insights: Categorized analysis of trending themes
 * 
 * Use cases:
 * - Market sentiment analysis
 * - Identifying emerging trends and narratives
 * - Content strategy for crypto projects
 * - Understanding community concerns and interests
 * - Predicting potential market movements based on discussion themes
 */
