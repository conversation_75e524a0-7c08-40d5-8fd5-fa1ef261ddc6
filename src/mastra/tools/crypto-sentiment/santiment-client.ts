/**
 * Santiment GraphQL API client for cryptocurrency sentiment analysis
 */

import type { SantimentApiResponse, SantimentQueryParams } from './types';

export class SantimentClient {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://api.santiment.net/graphql';

  constructor() {
    const apiKey = process.env.SANTIMENT_API_KEY;
    if (!apiKey) {
      throw new Error('SANTIMENT_API_KEY environment variable is required');
    }
    this.apiKey = apiKey;
  }

  /**
   * Execute a GraphQL query against the Santiment API
   */
  async query<T = any>(query: string, variables: Record<string, any> = {}): Promise<SantimentApiResponse<T>> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Apikey ${this.apiKey}`,
        },
        body: JSON.stringify({
          query,
          variables,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.errors && result.errors.length > 0) {
        throw new Error(`GraphQL error: ${result.errors[0].message}`);
      }

      return result;
    } catch (error) {
      throw new Error(`Santiment API request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get sentiment balance for a cryptocurrency
   */
  async getSentimentBalance(params: SantimentQueryParams) {
    const query = `
      query GetSentimentBalance($slug: String!, $from: DateTime!, $to: DateTime!, $interval: String!) {
        sentimentBalance(
          slug: $slug
          from: $from
          to: $to
          interval: $interval
        ) {
          datetime
          sentimentBalanceTotal
          sentimentBalanceTwitter
          sentimentBalanceReddit
          sentimentBalanceTelegram
        }
      }
    `;

    return this.query(query, {
      slug: params.slug,
      from: params.from,
      to: params.to,
      interval: params.interval || '1d',
    });
  }

  /**
   * Get social volume (mentions count) for a cryptocurrency
   */
  async getSocialVolume(params: SantimentQueryParams) {
    const query = `
      query GetSocialVolume($slug: String!, $from: DateTime!, $to: DateTime!, $interval: String!) {
        socialVolume(
          slug: $slug
          from: $from
          to: $to
          interval: $interval
        ) {
          datetime
          mentionsCount
        }
      }
    `;

    return this.query(query, {
      slug: params.slug,
      from: params.from,
      to: params.to,
      interval: params.interval || '1d',
    });
  }

  /**
   * Get trending words in crypto discussions
   */
  async getTrendingWords(params: Omit<SantimentQueryParams, 'slug'> & { size?: number }) {
    const query = `
      query GetTrendingWords($from: DateTime!, $to: DateTime!, $size: Int!) {
        getTrendingWords(
          from: $from
          to: $to
          size: $size
        ) {
          datetime
          topWords {
            word
            score
          }
        }
      }
    `;

    return this.query(query, {
      from: params.from,
      to: params.to,
      size: params.size || 10,
    });
  }

  /**
   * Get social dominance for a cryptocurrency
   */
  async getSocialDominance(params: SantimentQueryParams) {
    const query = `
      query GetSocialDominance($slug: String!, $from: DateTime!, $to: DateTime!, $interval: String!) {
        socialDominance(
          slug: $slug
          from: $from
          to: $to
          interval: $interval
        ) {
          datetime
          socialDominance
        }
      }
    `;

    return this.query(query, {
      slug: params.slug,
      from: params.from,
      to: params.to,
      interval: params.interval || '1d',
    });
  }
}
