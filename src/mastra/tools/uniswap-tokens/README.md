# Uniswap Token Analysis Tools

A comprehensive suite of tools for analyzing Uniswap V3 tokens, providing real-time market data, token rankings, and comparative analysis through The Graph's decentralized network.

## Overview

These tools provide deep insights into the Uniswap V3 ecosystem by analyzing on-chain data including trading volumes, price movements, liquidity metrics, and token performance across multiple timeframes.

## Tools

### 1. Get Top Uniswap Tokens (`get-top-uniswap-tokens`)

Retrieves and ranks the top Uniswap tokens based on configurable criteria.

**Features:**
- Configurable ranking by volume, price changes, or TVL
- Support for 24h, 7d, and 30d timeframes
- Historical price change calculations
- Minimum volume filtering
- Comprehensive market overview

**Parameters:**
- `limit` (1-100): Number of tokens to retrieve
- `sortBy`: Primary ranking criteria (volume24h, volumeUSD, totalValueLockedUSD, priceChange24h, priceChange7d)
- `timeframe`: Analysis timeframe (24h, 7d, 30d)
- `includeHistorical`: Include price change calculations
- `minVolume`: Minimum 24h volume filter

**Example:**
```typescript
const topTokens = await uniswapTokenTools.getTopUniswapTokens.execute({
  context: {
    limit: 50,
    sortBy: "volumeUSD",
    timeframe: "24h",
    includeHistorical: true,
    minVolume: 10000
  }
});
```

### 2. Get Token Details (`get-token-details`)

Provides comprehensive analysis for a specific token.

**Features:**
- Complete token metadata and metrics
- Historical price and volume analysis
- Top liquidity pools information
- Formatted metrics for easy reading
- Detailed performance summary

**Parameters:**
- `tokenAddress`: Ethereum address of the token
- `includeHistorical`: Include price change data
- `includePools`: Include pool information
- `historicalDays`: Days of historical data (1-365)

**Example:**
```typescript
const tokenDetails = await uniswapTokenTools.getTokenDetails.execute({
  context: {
    tokenAddress: "******************************************", // UNI token
    includeHistorical: true,
    includePools: true,
    historicalDays: 30
  }
});
```

### 3. Compare Tokens (`compare-tokens`)

Side-by-side comparison of multiple tokens with relative performance analysis.

**Features:**
- Multi-token comparison (2-10 tokens)
- Relative ranking across metrics
- Performance spread analysis
- Market insights and patterns
- Comprehensive comparison summary

**Parameters:**
- `tokenAddresses`: Array of token addresses (2-10)
- `includeHistorical`: Include price change data
- `comparisonMetrics`: Focus metrics for comparison

**Example:**
```typescript
const comparison = await uniswapTokenTools.compareTokens.execute({
  context: {
    tokenAddresses: [
      "******************************************", // UNI
      "0x7fc66500c84a76ad7e9c93437bfc5ac33e2ddae9", // AAVE
      "0xc00e94cb662c3520282e6f5717214004a7f26888"  // COMP
    ],
    includeHistorical: true,
    comparisonMetrics: ["volume", "priceChange", "tvl"]
  }
});
```

## Data Sources

### Uniswap V3 Subgraph
- **Endpoint**: The Graph's decentralized network
- **Data**: Real-time on-chain Uniswap V3 data
- **Update Frequency**: Near real-time (< 1 minute delay)
- **Coverage**: All Uniswap V3 tokens and pools

### Metrics Provided

#### Volume Metrics
- 24h, 7d, 30d trading volumes
- Volume trends and patterns
- Relative volume comparisons

#### Price Metrics
- Current derived ETH price
- 1h, 24h, 7d price changes
- Historical price analysis
- Price volatility indicators

#### Liquidity Metrics
- Total Value Locked (TVL)
- Pool count and distribution
- Liquidity concentration
- Fee tier analysis

#### Activity Metrics
- Transaction counts
- Trading frequency
- Pool activity levels
- Market participation

## Configuration

### Environment Variables

```bash
# Optional - for higher rate limits
THE_GRAPH_API_KEY=your_graph_api_key_here
```

### Rate Limits
- **With API Key**: 1000 requests/minute
- **Without API Key**: 100 requests/minute (public endpoint)

### Caching
- **Default TTL**: 5 minutes for rankings, 3 minutes for details
- **Cache Strategy**: In-memory (Redis recommended for production)
- **Cache Keys**: Based on parameters and timestamps

## Usage with Mastra Agent

The tools are designed to work with the Uniswap Token Analysis Agent:

```typescript
import { uniswapAgent } from './agents/uniswap-agent';

// Natural language queries
const response = await uniswapAgent.generate(
  "What are the top 20 Uniswap tokens by 24h volume and how have their prices changed?"
);

// Specific analysis requests
const analysis = await uniswapAgent.generate(
  "Compare UNI, AAVE, and COMP tokens and tell me which has the best performance this week"
);
```

**Example queries:**
- "Show me the top 50 tokens by trading volume"
- "What's the detailed analysis for the UNI token?"
- "Compare WETH, USDC, and WBTC performance"
- "Which tokens have the highest price increases today?"
- "What are the most liquid tokens on Uniswap?"

## Common Token Addresses

```typescript
const commonTokens = {
  WETH: '******************************************',
  USDC: '******************************************',
  USDT: '******************************************',
  UNI: '******************************************',
  WBTC: '******************************************',
  // ... more tokens
};
```

## Error Handling

The tools include comprehensive error handling for:
- Invalid token addresses
- Network connectivity issues
- API rate limiting
- Missing or incomplete data
- Cache failures

## Performance Considerations

### Optimization Strategies
- Intelligent caching with configurable TTL
- Batch requests for multiple tokens
- Efficient GraphQL queries
- Rate limit management
- Error recovery mechanisms

### Best Practices
- Use caching for frequently accessed data
- Implement proper error handling
- Monitor API usage and rate limits
- Consider data freshness requirements
- Use appropriate timeframes for analysis

## Integration

### Direct Tool Usage

```typescript
import { uniswapTokenTools } from './tools/uniswap-tokens';

// Get top tokens
const topTokens = await uniswapTokenTools.getTopUniswapTokens.execute({
  context: { limit: 20, sortBy: "volumeUSD" }
});

// Get token details
const details = await uniswapTokenTools.getTokenDetails.execute({
  context: { tokenAddress: "******************************************" }
});
```

### Agent Integration

```typescript
import { uniswapAgent } from './agents/uniswap-agent';

const analysis = await uniswapAgent.generate(
  "Analyze the top DeFi tokens and their recent performance"
);
```

## Supported Networks

- **Ethereum Mainnet**: Primary support
- **Future**: Polygon, Arbitrum, Optimism (planned)

## Dependencies

- `@mastra/core`: Tool framework
- `zod`: Input/output validation
- `fetch`: HTTP requests
- Native JavaScript: Date/time handling

## Contributing

When adding new features:
1. Follow existing patterns for tool structure
2. Include comprehensive error handling
3. Add proper TypeScript types
4. Update documentation and examples
5. Test with various token addresses and scenarios

## Disclaimer

This tool provides data analysis for informational purposes only. Cryptocurrency investments carry significant risks, and users should conduct their own research and consider their risk tolerance before making any investment decisions. Past performance does not guarantee future results.
