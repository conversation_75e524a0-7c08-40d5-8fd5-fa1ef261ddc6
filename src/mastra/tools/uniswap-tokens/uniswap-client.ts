/**
 * Uniswap V3 Subgraph GraphQL API client for token data retrieval
 */

import type {
    PoolsResponse,
    TokenDayDataResponse,
    TokensResponse,
    UniswapApiResponse,
    UniswapQueryParams
} from './types';

export class UniswapClient {
  private readonly baseUrl: string;
  private readonly apiKey?: string;

  constructor() {
    this.apiKey = process.env.THE_GRAPH_API_KEY;

    if (this.apiKey) {
      // Use The Graph's decentralized network endpoint for Uniswap V3
      this.baseUrl = `https://gateway.thegraph.com/api/${this.apiKey}/subgraphs/id/5zvR82QoaXYFyDEKLZ9t6v9adgnptxYpKpSbxtgVENFV`;
    } else {
      console.warn('THE_GRAPH_API_KEY not found. For production use, please get an API key from https://thegraph.com/');
      console.warn('Using mock data for demonstration purposes.');
      // For demo purposes, we'll use mock data when no API key is available
      this.baseUrl = 'mock://uniswap-v3';
    }
  }

  /**
   * Execute a GraphQL query against the Uniswap V3 subgraph
   */
  async query<T = any>(query: string, variables: Record<string, any> = {}): Promise<UniswapApiResponse<T>> {
    try {
      // Handle mock data for demo purposes
      if (this.baseUrl.startsWith('mock://')) {
        return this.getMockData(query, variables);
      }

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          variables,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.errors && result.errors.length > 0) {
        throw new Error(`GraphQL error: ${result.errors[0].message}`);
      }

      return result;
    } catch (error) {
      throw new Error(`Uniswap API request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate mock data for demonstration purposes
   */
  private getMockData(query: string, variables: Record<string, any>): UniswapApiResponse<any> {
    console.log('🎭 Using mock data for demonstration');

    // Mock token data based on real Uniswap tokens
    const mockTokens = [
      {
        id: '******************************************',
        symbol: 'WETH',
        name: 'Wrapped Ether',
        decimals: '18',
        totalValueLocked: '2500000.123456789',
        totalValueLockedUSD: '8750000000.50',
        volume: '150000.789',
        volumeUSD: '525000000.25',
        feesUSD: '1575000.75',
        poolCount: '1250',
        txCount: '2500000',
        derivedETH: '1.0',
      },
      {
        id: '******************************************',
        symbol: 'USDC',
        name: 'USD Coin',
        decimals: '6',
        totalValueLocked: '1800000000.123456',
        totalValueLockedUSD: '1800000000.12',
        volume: '200000000.456',
        volumeUSD: '200000000.46',
        feesUSD: '600000.14',
        poolCount: '980',
        txCount: '1800000',
        derivedETH: '0.000285714',
      },
      {
        id: '******************************************',
        symbol: 'UNI',
        name: 'Uniswap',
        decimals: '18',
        totalValueLocked: '15000000.789',
        totalValueLockedUSD: '180000000.95',
        volume: '5000000.123',
        volumeUSD: '60000000.48',
        feesUSD: '180000.14',
        poolCount: '450',
        txCount: '850000',
        derivedETH: '0.003428571',
      },
      {
        id: '******************************************',
        symbol: 'WBTC',
        name: 'Wrapped BTC',
        decimals: '8',
        totalValueLocked: '8500.123456789',
        totalValueLockedUSD: '850000000.12',
        volume: '1200.456789',
        volumeUSD: '120000000.68',
        feesUSD: '360000.20',
        poolCount: '320',
        txCount: '450000',
        derivedETH: '28.571428571',
      },
      {
        id: '******************************************',
        symbol: 'AAVE',
        name: 'Aave Token',
        decimals: '18',
        totalValueLocked: '2500000.456789',
        totalValueLockedUSD: '400000000.78',
        volume: '800000.123456',
        volumeUSD: '128000000.20',
        feesUSD: '384000.60',
        poolCount: '180',
        txCount: '320000',
        derivedETH: '0.045714286',
      },
    ];

    // Determine what type of query this is and return appropriate mock data
    if (query.includes('tokens(')) {
      const first = variables.first || 5;
      const tokens = mockTokens.slice(0, first);

      return {
        data: { tokens }
      };
    }

    if (query.includes('token(')) {
      const tokenId = variables.id?.toLowerCase();
      const token = mockTokens.find(t => t.id.toLowerCase() === tokenId) || mockTokens[0];

      return {
        data: { token }
      };
    }

    if (query.includes('tokenDayDatas')) {
      // Generate mock historical data
      const mockDayData = [];
      const now = Math.floor(Date.now() / 1000);

      for (let i = 0; i < 30; i++) {
        const date = now - (i * 24 * 60 * 60);
        const basePrice = 3500 + (Math.random() - 0.5) * 200; // Mock ETH price around $3500

        mockDayData.push({
          id: `${variables.where?.token || mockTokens[0].id}-${date}`,
          date,
          token: {
            id: variables.where?.token || mockTokens[0].id,
            symbol: 'WETH',
            name: 'Wrapped Ether',
          },
          volume: (Math.random() * 1000000).toString(),
          volumeUSD: (Math.random() * 50000000).toString(),
          open: (basePrice * 0.98).toString(),
          high: (basePrice * 1.05).toString(),
          low: (basePrice * 0.95).toString(),
          close: basePrice.toString(),
          priceUSD: basePrice.toString(),
          totalValueLocked: (Math.random() * 5000000).toString(),
          totalValueLockedUSD: (Math.random() * 15000000000).toString(),
        });
      }

      return {
        data: { tokenDayDatas: mockDayData }
      };
    }

    if (query.includes('pools')) {
      const mockPools = [
        {
          id: '******************************************',
          token0: mockTokens[0], // WETH
          token1: mockTokens[1], // USDC
          feeTier: '3000',
          liquidity: '15000000000000000000',
          sqrtPrice: '1987123456789012345678901234567890',
          tick: '202200',
          token0Price: '3500.123456',
          token1Price: '0.000285714',
          volumeUSD: '25000000.50',
          totalValueLockedUSD: '175000000.25',
          txCount: '125000',
        },
        {
          id: '******************************************',
          token0: mockTokens[0], // WETH
          token1: mockTokens[2], // UNI
          feeTier: '3000',
          liquidity: '8500000000000000000',
          sqrtPrice: '987654321098765432109876543210',
          tick: '185400',
          token0Price: '291.234567',
          token1Price: '0.003434343',
          volumeUSD: '8500000.75',
          totalValueLockedUSD: '42500000.88',
          txCount: '65000',
        },
      ];

      return {
        data: { pools: mockPools }
      };
    }

    // Default empty response
    return {
      data: {}
    };
  }

  /**
   * Get top tokens by various metrics
   */
  async getTopTokens(params: UniswapQueryParams = {}): Promise<TokensResponse> {
    const {
      first = 100,
      skip = 0,
      orderBy = 'volumeUSD',
      orderDirection = 'desc',
      where = {},
    } = params;

    const query = `
      query GetTopTokens($first: Int!, $skip: Int!, $orderBy: Token_orderBy!, $orderDirection: OrderDirection!, $where: Token_filter) {
        tokens(
          first: $first
          skip: $skip
          orderBy: $orderBy
          orderDirection: $orderDirection
          where: $where
        ) {
          id
          symbol
          name
          decimals
          totalValueLocked
          totalValueLockedUSD
          volume
          volumeUSD
          feesUSD
          poolCount
          txCount
          derivedETH
        }
      }
    `;

    return this.query(query, {
      first,
      skip,
      orderBy,
      orderDirection,
      where,
    });
  }

  /**
   * Get token details by address
   */
  async getTokenDetails(tokenAddress: string): Promise<{ token: any }> {
    const query = `
      query GetTokenDetails($id: ID!) {
        token(id: $id) {
          id
          symbol
          name
          decimals
          totalValueLocked
          totalValueLockedUSD
          volume
          volumeUSD
          feesUSD
          poolCount
          txCount
          derivedETH
        }
      }
    `;

    return this.query(query, { id: tokenAddress.toLowerCase() });
  }

  /**
   * Get historical token data for price calculations
   */
  async getTokenDayData(params: {
    tokenAddress?: string;
    days?: number;
    first?: number;
    orderBy?: string;
    orderDirection?: 'asc' | 'desc';
  }): Promise<TokenDayDataResponse> {
    const {
      tokenAddress,
      days = 30,
      first = 1000,
      orderBy = 'date',
      orderDirection = 'desc',
    } = params;

    const currentTimestamp = Math.floor(Date.now() / 1000);
    const startTimestamp = currentTimestamp - (days * 24 * 60 * 60);

    const whereClause = tokenAddress
      ? { token: tokenAddress.toLowerCase(), date_gte: startTimestamp }
      : { date_gte: startTimestamp };

    const query = `
      query GetTokenDayData($first: Int!, $orderBy: TokenDayData_orderBy!, $orderDirection: OrderDirection!, $where: TokenDayData_filter!) {
        tokenDayDatas(
          first: $first
          orderBy: $orderBy
          orderDirection: $orderDirection
          where: $where
        ) {
          id
          date
          token {
            id
            symbol
            name
          }
          volume
          volumeUSD
          open
          high
          low
          close
          priceUSD
          totalValueLocked
          totalValueLockedUSD
        }
      }
    `;

    return this.query(query, {
      first,
      orderBy,
      orderDirection,
      where: whereClause,
    });
  }

  /**
   * Get pools for a specific token
   */
  async getTokenPools(tokenAddress: string, first: number = 10): Promise<PoolsResponse> {
    const query = `
      query GetTokenPools($token0: String!, $token1: String!, $first: Int!) {
        pools(
          first: $first
          orderBy: totalValueLockedUSD
          orderDirection: desc
          where: {
            or: [
              { token0: $token0 },
              { token1: $token1 }
            ]
          }
        ) {
          id
          token0 {
            id
            symbol
            name
            decimals
          }
          token1 {
            id
            symbol
            name
            decimals
          }
          feeTier
          liquidity
          sqrtPrice
          tick
          token0Price
          token1Price
          volumeUSD
          totalValueLockedUSD
          txCount
        }
      }
    `;

    const tokenAddr = tokenAddress.toLowerCase();
    return this.query(query, {
      token0: tokenAddr,
      token1: tokenAddr,
      first,
    });
  }

  /**
   * Get multiple tokens by addresses
   */
  async getMultipleTokens(tokenAddresses: string[]): Promise<TokensResponse> {
    const addresses = tokenAddresses.map(addr => addr.toLowerCase());

    const query = `
      query GetMultipleTokens($addresses: [ID!]!) {
        tokens(where: { id_in: $addresses }) {
          id
          symbol
          name
          decimals
          totalValueLocked
          totalValueLockedUSD
          volume
          volumeUSD
          feesUSD
          poolCount
          txCount
          derivedETH
        }
      }
    `;

    return this.query(query, { addresses });
  }
}
