/**
 * Get top Uniswap tokens with comprehensive ranking and metrics
 */

import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import type { CacheEntry, RankingCriteria } from "./types";
import { UniswapClient } from "./uniswap-client";
import {
    calculatePriceChanges,
    calculateVolumeMetrics,
    createCacheEntry,
    createCacheKey,
    formatNumber,
    formatPercentageChange,
    isCacheValid,
    rankTokens
} from "./utils";

// Simple in-memory cache (in production, use Redis/Upstash)
const cache = new Map<string, CacheEntry<any>>();

export const getTopUniswapTokensTool = createTool({
  id: "get-top-uniswap-tokens",
  description: "Retrieve and rank the top Uniswap tokens by trading volume, price changes, and liquidity metrics. Returns comprehensive data including 24h/7d/30d volumes, price changes, and market metrics.",
  inputSchema: z.object({
    limit: z.number().min(1).max(100).default(50).describe("Number of top tokens to retrieve (default: 50, max: 100)"),
    sortBy: z.enum(["volume24h", "volumeUSD", "totalValueLockedUSD", "priceChange24h", "priceChange7d"]).default("volumeUSD").describe("Primary sorting criteria (default: volumeUSD)"),
    timeframe: z.enum(["24h", "7d", "30d"]).default("24h").describe("Timeframe for volume and price data (default: 24h)"),
    includeHistorical: z.boolean().default(true).describe("Include historical price change calculations (default: true)"),
    minVolume: z.number().min(0).default(1000).describe("Minimum 24h volume in USD to include token (default: 1000)"),
  }),
  outputSchema: z.object({
    totalTokens: z.number(),
    timeframe: z.string(),
    sortCriteria: z.string(),
    topTokens: z.array(z.object({
      rank: z.number(),
      address: z.string(),
      symbol: z.string(),
      name: z.string(),
      metrics: z.object({
        volume24hUSD: z.number(),
        priceChange24h: z.number().optional(),
        priceChange7d: z.number().optional(),
        totalValueLockedUSD: z.number(),
        poolCount: z.number(),
        txCount: z.number(),
      }),
      formattedMetrics: z.object({
        volume24h: z.string(),
        priceChange24h: z.string(),
        priceChange7d: z.string(),
        tvl: z.string(),
      }),
      summary: z.string(),
    })),
    marketOverview: z.string(),
    lastUpdated: z.string(),
  }),
  execute: async ({ context }) => {
    try {
      const cacheKey = createCacheKey('top-tokens', context);
      const cachedResult = cache.get(cacheKey);

      if (isCacheValid(cachedResult)) {
        return cachedResult.data;
      }

      // Initialize Uniswap client
      const client = new UniswapClient();

      // Fetch top tokens by volume
      const tokensResponse = await client.getTopTokens({
        first: Math.min(context.limit * 2, 200), // Fetch more to filter by volume
        orderBy: 'volumeUSD',
        orderDirection: 'desc',
        where: {
          volumeUSD_gte: context.minVolume.toString(),
        },
      });

      if (!tokensResponse.data?.tokens || tokensResponse.data.tokens.length === 0) {
        throw new Error('No tokens found matching the criteria');
      }

      let tokens = tokensResponse.data.tokens;

      // If historical data is requested, fetch price changes
      if (context.includeHistorical) {
        const historicalPromises = tokens.slice(0, context.limit).map(async (token) => {
          try {
            const dayDataResponse = await client.getTokenDayData({
              tokenAddress: token.id,
              days: 30, // Get 30 days for 7d and 24h calculations
              first: 30,
            });

            if (dayDataResponse.data?.tokenDayDatas) {
              const currentPrice = parseFloat(token.derivedETH) || 0;
              const priceChanges = calculatePriceChanges(currentPrice, dayDataResponse.data.tokenDayDatas);
              const volumeMetrics = calculateVolumeMetrics(dayDataResponse.data.tokenDayDatas);

              return {
                ...token,
                ...priceChanges,
                ...volumeMetrics,
              };
            }
            return token;
          } catch (error) {
            console.warn(`Failed to fetch historical data for ${token.symbol}:`, error);
            return token;
          }
        });

        tokens = await Promise.all(historicalPromises);
      }

      // Define ranking criteria
      const rankingCriteria: RankingCriteria = {
        primary: context.sortBy,
        secondary: context.sortBy !== 'priceChange24h' ? 'priceChange24h' : 'totalValueLockedUSD',
        tertiary: 'totalValueLockedUSD',
      };

      // Rank tokens
      const rankedTokens = rankTokens(tokens.slice(0, context.limit), rankingCriteria);

      // Format results
      const topTokens = rankedTokens.map(ranking => ({
        rank: ranking.rank,
        address: ranking.token.id,
        symbol: ranking.token.symbol,
        name: ranking.token.name,
        metrics: {
          volume24hUSD: ranking.metrics.volume24hUSD,
          priceChange24h: ranking.metrics.priceChange24h,
          priceChange7d: ranking.metrics.priceChange7d,
          totalValueLockedUSD: ranking.metrics.totalValueLockedUSD,
          poolCount: parseFloat(ranking.token.poolCount || '0'),
          txCount: parseFloat(ranking.token.txCount || '0'),
        },
        formattedMetrics: {
          volume24h: formatNumber(ranking.metrics.volume24hUSD),
          priceChange24h: formatPercentageChange(ranking.metrics.priceChange24h),
          priceChange7d: formatPercentageChange(ranking.metrics.priceChange7d),
          tvl: formatNumber(ranking.metrics.totalValueLockedUSD),
        },
        summary: `${ranking.token.name} (${ranking.token.symbol}) - Rank #${ranking.rank}: ${formatNumber(ranking.metrics.volume24hUSD)} 24h volume, ${formatPercentageChange(ranking.metrics.priceChange24h)} 24h change`,
      }));

      // Generate market overview
      const totalVolume = rankedTokens.reduce((sum, t) => sum + t.metrics.volume24hUSD, 0);
      const avgPriceChange = rankedTokens.reduce((sum, t) => sum + t.metrics.priceChange24h, 0) / rankedTokens.length;
      const totalTVL = rankedTokens.reduce((sum, t) => sum + t.metrics.totalValueLockedUSD, 0);

      const marketOverview = `Top ${context.limit} Uniswap tokens analysis: ${formatNumber(totalVolume)} total 24h volume, ${formatNumber(totalTVL)} total TVL, ${formatPercentageChange(avgPriceChange)} average 24h price change. Leading token: ${topTokens[0]?.name} with ${topTokens[0]?.formattedMetrics.volume24h} volume.`;

      const result = {
        totalTokens: topTokens.length,
        timeframe: context.timeframe,
        sortCriteria: context.sortBy,
        topTokens,
        marketOverview,
        lastUpdated: new Date().toISOString(),
      };

      // Cache the result for 5 minutes
      cache.set(cacheKey, createCacheEntry(result, 5));

      return result;

    } catch (error) {
      throw new Error(`Failed to retrieve top Uniswap tokens: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});
