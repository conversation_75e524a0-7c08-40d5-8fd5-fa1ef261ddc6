/**
 * Get detailed information for a specific Uniswap token
 */

import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import type { CacheEntry } from "./types";
import { UniswapClient } from "./uniswap-client";
import {
    calculatePriceChanges,
    calculateVolumeMetrics,
    createCacheEntry,
    createCacheKey,
    formatNumber,
    formatPercentageChange,
    isCacheValid,
    normalizeTokenAddress
} from "./utils";

// Simple in-memory cache
const cache = new Map<string, CacheEntry<any>>();

export const getTokenDetailsTool = createTool({
  id: "get-token-details",
  description: "Get comprehensive details for a specific Uniswap token including current metrics, historical price changes, volume data, and liquidity information.",
  inputSchema: z.object({
    tokenAddress: z.string().describe("Ethereum address of the token (e.g., '******************************************' for UNI)"),
    includeHistorical: z.boolean().default(true).describe("Include historical price and volume data (default: true)"),
    includePools: z.boolean().default(true).describe("Include information about token's liquidity pools (default: true)"),
    historicalDays: z.number().min(1).max(365).default(30).describe("Number of days of historical data to analyze (default: 30)"),
  }),
  outputSchema: z.object({
    tokenAddress: z.string(),
    basicInfo: z.object({
      symbol: z.string(),
      name: z.string(),
      decimals: z.number(),
    }),
    currentMetrics: z.object({
      totalValueLockedUSD: z.number(),
      volume24hUSD: z.number(),
      poolCount: z.number(),
      txCount: z.number(),
      derivedETH: z.number(),
    }),
    priceChanges: z.object({
      change24h: z.number().optional(),
      change7d: z.number().optional(),
      change30d: z.number().optional(),
    }).optional(),
    volumeMetrics: z.object({
      volume24h: z.string(),
      volume7d: z.string(),
      volume30d: z.string(),
    }).optional(),
    formattedMetrics: z.object({
      tvl: z.string(),
      volume24h: z.string(),
      priceChange24h: z.string(),
      priceChange7d: z.string(),
    }),
    topPools: z.array(z.object({
      poolAddress: z.string(),
      pairedToken: z.object({
        address: z.string(),
        symbol: z.string(),
        name: z.string(),
      }),
      feeTier: z.string(),
      tvlUSD: z.number(),
      volume24hUSD: z.number(),
      token0Price: z.string(),
      token1Price: z.string(),
    })).optional(),
    summary: z.string(),
    lastUpdated: z.string(),
  }),
  execute: async ({ context }) => {
    try {
      // Validate and normalize token address
      const tokenAddress = normalizeTokenAddress(context.tokenAddress);

      const cacheKey = createCacheKey('token-details', { ...context, tokenAddress });
      const cachedResult = cache.get(cacheKey);

      if (isCacheValid(cachedResult)) {
        return cachedResult.data;
      }

      // Initialize Uniswap client
      const client = new UniswapClient();

      // Fetch basic token data
      const tokenResponse = await client.getTokenDetails(tokenAddress);

      if (!tokenResponse.data?.token) {
        throw new Error(`Token not found: ${context.tokenAddress}`);
      }

      const token = tokenResponse.data.token;

      // Prepare result object
      const result: any = {
        tokenAddress: token.id,
        basicInfo: {
          symbol: token.symbol,
          name: token.name,
          decimals: parseInt(token.decimals),
        },
        currentMetrics: {
          totalValueLockedUSD: parseFloat(token.totalValueLockedUSD || '0'),
          volume24hUSD: parseFloat(token.volumeUSD || '0'),
          poolCount: parseInt(token.poolCount || '0'),
          txCount: parseInt(token.txCount || '0'),
          derivedETH: parseFloat(token.derivedETH || '0'),
        },
        formattedMetrics: {
          tvl: formatNumber(parseFloat(token.totalValueLockedUSD || '0')),
          volume24h: formatNumber(parseFloat(token.volumeUSD || '0')),
          priceChange24h: 'N/A',
          priceChange7d: 'N/A',
        },
        lastUpdated: new Date().toISOString(),
      };

      // Fetch historical data if requested
      if (context.includeHistorical) {
        try {
          const dayDataResponse = await client.getTokenDayData({
            tokenAddress,
            days: context.historicalDays,
            first: context.historicalDays,
          });

          if (dayDataResponse.data?.tokenDayDatas && dayDataResponse.data.tokenDayDatas.length > 0) {
            const currentPrice = parseFloat(token.derivedETH) || 0;
            const priceChanges = calculatePriceChanges(currentPrice, dayDataResponse.data.tokenDayDatas);
            const volumeMetrics = calculateVolumeMetrics(dayDataResponse.data.tokenDayDatas);

            result.priceChanges = {
              change24h: priceChanges.priceChange24h,
              change7d: priceChanges.priceChange7d,
            };

            result.volumeMetrics = volumeMetrics;

            // Update formatted metrics
            result.formattedMetrics.priceChange24h = priceChanges.priceChange24h
              ? formatPercentageChange(priceChanges.priceChange24h)
              : 'N/A';
            result.formattedMetrics.priceChange7d = priceChanges.priceChange7d
              ? formatPercentageChange(priceChanges.priceChange7d)
              : 'N/A';
          }
        } catch (error) {
          console.warn(`Failed to fetch historical data for ${token.symbol}:`, error);
        }
      }

      // Fetch pool data if requested
      if (context.includePools) {
        try {
          const poolsResponse = await client.getTokenPools(tokenAddress, 5);

          if (poolsResponse.data?.pools && poolsResponse.data.pools.length > 0) {
            result.topPools = poolsResponse.data.pools.map(pool => {
              // Determine which token is the paired token (not our target token)
              const isToken0 = pool.token0.id.toLowerCase() === tokenAddress;
              const pairedToken = isToken0 ? pool.token1 : pool.token0;

              return {
                poolAddress: pool.id,
                pairedToken: {
                  address: pairedToken.id,
                  symbol: pairedToken.symbol,
                  name: pairedToken.name,
                },
                feeTier: `${parseInt(pool.feeTier) / 10000}%`, // Convert from basis points
                tvlUSD: parseFloat(pool.totalValueLockedUSD || '0'),
                volume24hUSD: parseFloat(pool.volumeUSD || '0'),
                token0Price: pool.token0Price,
                token1Price: pool.token1Price,
              };
            });
          }
        } catch (error) {
          console.warn(`Failed to fetch pool data for ${token.symbol}:`, error);
        }
      }

      // Generate summary
      const priceChange24h = result.priceChanges?.change24h
        ? formatPercentageChange(result.priceChanges.change24h)
        : 'N/A';

      const topPool = result.topPools?.[0];
      const poolInfo = topPool
        ? ` Main pool: ${topPool.pairedToken.symbol} (${formatNumber(topPool.tvlUSD)} TVL).`
        : '';

      result.summary = `${token.name} (${token.symbol}): ${result.formattedMetrics.tvl} total value locked, ${result.formattedMetrics.volume24h} 24h volume, ${priceChange24h} 24h price change. Active in ${result.currentMetrics.poolCount} pools with ${result.currentMetrics.txCount} total transactions.${poolInfo}`;

      // Cache the result for 3 minutes
      cache.set(cacheKey, createCacheEntry(result, 3));

      return result;

    } catch (error) {
      throw new Error(`Failed to retrieve token details: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});
