/**
 * Compare multiple Uniswap tokens side by side
 */

import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import type { CacheEntry } from "./types";
import { UniswapClient } from "./uniswap-client";
import {
    calculatePriceChanges,
    calculateVolumeMetrics,
    createCacheEntry,
    createCacheKey,
    formatNumber,
    formatPercentageChange,
    isCacheValid,
    normalizeTokenAddress
} from "./utils";

// Simple in-memory cache
const cache = new Map<string, CacheEntry<any>>();

export const compareTokensTool = createTool({
  id: "compare-tokens",
  description: "Compare multiple Uniswap tokens side by side with detailed metrics including volume, price changes, liquidity, and relative performance analysis.",
  inputSchema: z.object({
    tokenAddresses: z.array(z.string()).min(2).max(10).describe("Array of Ethereum token addresses to compare (2-10 tokens)"),
    includeHistorical: z.boolean().default(true).describe("Include historical price change data (default: true)"),
    comparisonMetrics: z.array(z.enum(["volume", "priceChange", "tvl", "poolCount", "txCount"])).default(["volume", "priceChange", "tvl"]).describe("Metrics to focus on in comparison"),
  }),
  outputSchema: z.object({
    totalTokens: z.number(),
    comparisonDate: z.string(),
    tokens: z.array(z.object({
      address: z.string(),
      symbol: z.string(),
      name: z.string(),
      metrics: z.object({
        volume24hUSD: z.number(),
        priceChange24h: z.number().optional(),
        priceChange7d: z.number().optional(),
        totalValueLockedUSD: z.number(),
        poolCount: z.number(),
        txCount: z.number(),
      }),
      formattedMetrics: z.object({
        volume24h: z.string(),
        priceChange24h: z.string(),
        priceChange7d: z.string(),
        tvl: z.string(),
      }),
      rank: z.object({
        byVolume: z.number(),
        byPriceChange24h: z.number(),
        byTVL: z.number(),
      }),
    })),
    comparison: z.object({
      volume: z.object({
        highest: z.object({
          symbol: z.string(),
          value: z.string(),
        }),
        lowest: z.object({
          symbol: z.string(),
          value: z.string(),
        }),
        ratio: z.string(),
      }),
      priceChange24h: z.object({
        highest: z.object({
          symbol: z.string(),
          value: z.string(),
        }),
        lowest: z.object({
          symbol: z.string(),
          value: z.string(),
        }),
        spread: z.string(),
      }),
      tvl: z.object({
        highest: z.object({
          symbol: z.string(),
          value: z.string(),
        }),
        lowest: z.object({
          symbol: z.string(),
          value: z.string(),
        }),
        ratio: z.string(),
      }),
    }),
    insights: z.array(z.string()),
    summary: z.string(),
  }),
  execute: async ({ context }) => {
    try {
      // Validate and normalize token addresses
      const tokenAddresses = context.tokenAddresses.map(addr => normalizeTokenAddress(addr));

      const cacheKey = createCacheKey('compare-tokens', { ...context, tokenAddresses });
      const cachedResult = cache.get(cacheKey);

      if (isCacheValid(cachedResult)) {
        return cachedResult.data;
      }

      // Initialize Uniswap client
      const client = new UniswapClient();

      // Fetch token data for all addresses
      const tokensResponse = await client.getMultipleTokens(tokenAddresses);

      if (!tokensResponse.data?.tokens || tokensResponse.data.tokens.length === 0) {
        throw new Error('No tokens found for the provided addresses');
      }

      let tokens = tokensResponse.data.tokens;

      // Ensure we have all requested tokens
      const foundAddresses = tokens.map(t => t.id.toLowerCase());
      const missingAddresses = tokenAddresses.filter(addr => !foundAddresses.includes(addr));

      if (missingAddresses.length > 0) {
        console.warn(`Some tokens not found: ${missingAddresses.join(', ')}`);
      }

      // Fetch historical data if requested
      if (context.includeHistorical) {
        const historicalPromises = tokens.map(async (token) => {
          try {
            const dayDataResponse = await client.getTokenDayData({
              tokenAddress: token.id,
              days: 30,
              first: 30,
            });

            if (dayDataResponse.data?.tokenDayDatas) {
              const currentPrice = parseFloat(token.derivedETH) || 0;
              const priceChanges = calculatePriceChanges(currentPrice, dayDataResponse.data.tokenDayDatas);
              const volumeMetrics = calculateVolumeMetrics(dayDataResponse.data.tokenDayDatas);

              return {
                ...token,
                ...priceChanges,
                ...volumeMetrics,
              };
            }
            return token;
          } catch (error) {
            console.warn(`Failed to fetch historical data for ${token.symbol}:`, error);
            return token;
          }
        });

        tokens = await Promise.all(historicalPromises);
      }

      // Calculate metrics for each token
      const tokenMetrics = tokens.map(token => ({
        address: token.id,
        symbol: token.symbol,
        name: token.name,
        metrics: {
          volume24hUSD: parseFloat(token.volume24h || token.volumeUSD || '0'),
          priceChange24h: token.priceChange24h,
          priceChange7d: token.priceChange7d,
          totalValueLockedUSD: parseFloat(token.totalValueLockedUSD || '0'),
          poolCount: parseInt(token.poolCount || '0'),
          txCount: parseInt(token.txCount || '0'),
        },
        formattedMetrics: {
          volume24h: formatNumber(parseFloat(token.volume24h || token.volumeUSD || '0')),
          priceChange24h: token.priceChange24h ? formatPercentageChange(token.priceChange24h) : 'N/A',
          priceChange7d: token.priceChange7d ? formatPercentageChange(token.priceChange7d) : 'N/A',
          tvl: formatNumber(parseFloat(token.totalValueLockedUSD || '0')),
        },
      }));

      // Calculate rankings
      const tokensWithRanks = tokenMetrics.map(token => {
        // Rank by volume (descending)
        const volumeRank = tokenMetrics
          .sort((a, b) => b.metrics.volume24hUSD - a.metrics.volume24hUSD)
          .findIndex(t => t.address === token.address) + 1;

        // Rank by price change 24h (descending)
        const priceChangeRank = tokenMetrics
          .filter(t => t.metrics.priceChange24h !== undefined)
          .sort((a, b) => (b.metrics.priceChange24h || 0) - (a.metrics.priceChange24h || 0))
          .findIndex(t => t.address === token.address) + 1;

        // Rank by TVL (descending)
        const tvlRank = tokenMetrics
          .sort((a, b) => b.metrics.totalValueLockedUSD - a.metrics.totalValueLockedUSD)
          .findIndex(t => t.address === token.address) + 1;

        return {
          ...token,
          rank: {
            byVolume: volumeRank,
            byPriceChange24h: priceChangeRank || tokenMetrics.length,
            byTVL: tvlRank,
          },
        };
      });

      // Calculate comparison metrics
      const volumeValues = tokenMetrics.map(t => t.metrics.volume24hUSD);
      const priceChangeValues = tokenMetrics.filter(t => t.metrics.priceChange24h !== undefined).map(t => t.metrics.priceChange24h!);
      const tvlValues = tokenMetrics.map(t => t.metrics.totalValueLockedUSD);

      const comparison = {
        volume: {
          highest: {
            symbol: tokenMetrics.find(t => t.metrics.volume24hUSD === Math.max(...volumeValues))!.symbol,
            value: formatNumber(Math.max(...volumeValues)),
          },
          lowest: {
            symbol: tokenMetrics.find(t => t.metrics.volume24hUSD === Math.min(...volumeValues))!.symbol,
            value: formatNumber(Math.min(...volumeValues)),
          },
          ratio: `${(Math.max(...volumeValues) / Math.min(...volumeValues)).toFixed(1)}x`,
        },
        priceChange24h: {
          highest: priceChangeValues.length > 0 ? {
            symbol: tokenMetrics.find(t => t.metrics.priceChange24h === Math.max(...priceChangeValues))!.symbol,
            value: formatPercentageChange(Math.max(...priceChangeValues)),
          } : { symbol: 'N/A', value: 'N/A' },
          lowest: priceChangeValues.length > 0 ? {
            symbol: tokenMetrics.find(t => t.metrics.priceChange24h === Math.min(...priceChangeValues))!.symbol,
            value: formatPercentageChange(Math.min(...priceChangeValues)),
          } : { symbol: 'N/A', value: 'N/A' },
          spread: priceChangeValues.length > 0 ?
            `${(Math.max(...priceChangeValues) - Math.min(...priceChangeValues)).toFixed(2)}%` : 'N/A',
        },
        tvl: {
          highest: {
            symbol: tokenMetrics.find(t => t.metrics.totalValueLockedUSD === Math.max(...tvlValues))!.symbol,
            value: formatNumber(Math.max(...tvlValues)),
          },
          lowest: {
            symbol: tokenMetrics.find(t => t.metrics.totalValueLockedUSD === Math.min(...tvlValues))!.symbol,
            value: formatNumber(Math.min(...tvlValues)),
          },
          ratio: `${(Math.max(...tvlValues) / Math.min(...tvlValues)).toFixed(1)}x`,
        },
      };

      // Generate insights
      const insights: string[] = [];

      if (comparison.volume.ratio !== 'Infinityx') {
        insights.push(`Volume leader ${comparison.volume.highest.symbol} has ${comparison.volume.ratio} more trading volume than ${comparison.volume.lowest.symbol}`);
      }

      if (priceChangeValues.length > 0) {
        insights.push(`Price performance spread: ${comparison.priceChange24h.spread} between best (${comparison.priceChange24h.highest.symbol}) and worst (${comparison.priceChange24h.lowest.symbol}) performers`);
      }

      if (comparison.tvl.ratio !== 'Infinityx') {
        insights.push(`Liquidity leader ${comparison.tvl.highest.symbol} has ${comparison.tvl.ratio} more TVL than ${comparison.tvl.lowest.symbol}`);
      }

      // Generate summary
      const summary = `Comparison of ${tokenMetrics.length} tokens: Volume leader is ${comparison.volume.highest.symbol} (${comparison.volume.highest.value}), best 24h performer is ${comparison.priceChange24h.highest.symbol} (${comparison.priceChange24h.highest.value}), and liquidity leader is ${comparison.tvl.highest.symbol} (${comparison.tvl.highest.value}).`;

      const result = {
        totalTokens: tokenMetrics.length,
        comparisonDate: new Date().toISOString(),
        tokens: tokensWithRanks,
        comparison,
        insights,
        summary,
      };

      // Cache the result for 3 minutes
      cache.set(cacheKey, createCacheEntry(result, 3));

      return result;

    } catch (error) {
      throw new Error(`Failed to compare tokens: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});
