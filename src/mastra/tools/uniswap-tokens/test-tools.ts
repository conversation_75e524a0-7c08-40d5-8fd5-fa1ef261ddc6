/**
 * Test suite for Uniswap token analysis tools
 * 
 * Run these tests to verify the tools are working correctly
 */

import { uniswapTokenTools } from "./index";
import { commonTokens } from "./index";

/**
 * Test the getTopUniswapTokens tool
 */
async function testGetTopTokens() {
  console.log("🧪 Testing getTopUniswapTokens...");
  
  try {
    const result = await uniswapTokenTools.getTopUniswapTokens.execute({
      context: {
        limit: 10,
        sortBy: "volumeUSD",
        timeframe: "24h",
        includeHistorical: true,
        minVolume: 1000,
      }
    });

    console.log("✅ getTopUniswapTokens test passed");
    console.log(`📊 Found ${result.totalTokens} tokens`);
    console.log(`🏆 Top token: ${result.topTokens[0]?.name} (${result.topTokens[0]?.symbol})`);
    console.log(`💰 Volume: ${result.topTokens[0]?.formattedMetrics.volume24h}`);
    console.log(`📈 Price change: ${result.topTokens[0]?.formattedMetrics.priceChange24h}`);
    console.log(`🔗 Market overview: ${result.marketOverview}`);
    
    return result;
  } catch (error) {
    console.error("❌ getTopUniswapTokens test failed:", error);
    throw error;
  }
}

/**
 * Test the getTokenDetails tool
 */
async function testGetTokenDetails() {
  console.log("\n🧪 Testing getTokenDetails...");
  
  try {
    const result = await uniswapTokenTools.getTokenDetails.execute({
      context: {
        tokenAddress: commonTokens.UNI,
        includeHistorical: true,
        includePools: true,
        historicalDays: 7,
      }
    });

    console.log("✅ getTokenDetails test passed");
    console.log(`🪙 Token: ${result.basicInfo.name} (${result.basicInfo.symbol})`);
    console.log(`💰 TVL: ${result.formattedMetrics.tvl}`);
    console.log(`📊 Volume: ${result.formattedMetrics.volume24h}`);
    console.log(`📈 Price change 24h: ${result.formattedMetrics.priceChange24h}`);
    console.log(`🏊 Pool count: ${result.currentMetrics.poolCount}`);
    
    if (result.topPools && result.topPools.length > 0) {
      console.log(`🔗 Top pool: ${result.topPools[0].pairedToken.symbol} (${result.topPools[0].feeTier})`);
    }
    
    return result;
  } catch (error) {
    console.error("❌ getTokenDetails test failed:", error);
    throw error;
  }
}

/**
 * Test the compareTokens tool
 */
async function testCompareTokens() {
  console.log("\n🧪 Testing compareTokens...");
  
  try {
    const result = await uniswapTokenTools.compareTokens.execute({
      context: {
        tokenAddresses: [
          commonTokens.UNI,
          commonTokens.WETH,
          commonTokens.USDC,
        ],
        includeHistorical: true,
        comparisonMetrics: ["volume", "priceChange", "tvl"],
      }
    });

    console.log("✅ compareTokens test passed");
    console.log(`🔢 Comparing ${result.totalTokens} tokens`);
    console.log(`🏆 Volume leader: ${result.comparison.volume.highest.symbol} (${result.comparison.volume.highest.value})`);
    console.log(`📈 Best performer: ${result.comparison.priceChange24h.highest.symbol} (${result.comparison.priceChange24h.highest.value})`);
    console.log(`🏊 Liquidity leader: ${result.comparison.tvl.highest.symbol} (${result.comparison.tvl.highest.value})`);
    console.log(`💡 Summary: ${result.summary}`);
    
    if (result.insights.length > 0) {
      console.log("🔍 Insights:");
      result.insights.forEach((insight, index) => {
        console.log(`   ${index + 1}. ${insight}`);
      });
    }
    
    return result;
  } catch (error) {
    console.error("❌ compareTokens test failed:", error);
    throw error;
  }
}

/**
 * Test error handling
 */
async function testErrorHandling() {
  console.log("\n🧪 Testing error handling...");
  
  try {
    // Test with invalid token address
    await uniswapTokenTools.getTokenDetails.execute({
      context: {
        tokenAddress: "0xinvalid",
      }
    });
    
    console.error("❌ Error handling test failed - should have thrown an error");
  } catch (error) {
    console.log("✅ Error handling test passed - correctly caught invalid address");
  }
  
  try {
    // Test with non-existent token
    await uniswapTokenTools.getTokenDetails.execute({
      context: {
        tokenAddress: "0x0000000000000000000000000000000000000000",
      }
    });
    
    console.log("⚠️ Non-existent token test - may pass if token exists or fail gracefully");
  } catch (error) {
    console.log("✅ Error handling test passed - correctly handled non-existent token");
  }
}

/**
 * Run all tests
 */
export async function runAllTests() {
  console.log("🚀 Starting Uniswap token tools tests...\n");
  
  try {
    await testGetTopTokens();
    await testGetTokenDetails();
    await testCompareTokens();
    await testErrorHandling();
    
    console.log("\n🎉 All tests completed successfully!");
    console.log("\n📝 Test Summary:");
    console.log("✅ getTopUniswapTokens - Retrieves and ranks top tokens");
    console.log("✅ getTokenDetails - Provides detailed token analysis");
    console.log("✅ compareTokens - Compares multiple tokens side-by-side");
    console.log("✅ Error handling - Properly handles invalid inputs");
    
  } catch (error) {
    console.error("\n💥 Test suite failed:", error);
    throw error;
  }
}

/**
 * Example usage patterns
 */
export function showUsageExamples() {
  console.log("\n📚 Usage Examples:");
  
  console.log("\n1. Get top tokens by volume:");
  console.log(`
const topTokens = await uniswapTokenTools.getTopUniswapTokens.execute({
  context: { limit: 20, sortBy: "volumeUSD", timeframe: "24h" }
});
  `);
  
  console.log("\n2. Analyze a specific token:");
  console.log(`
const details = await uniswapTokenTools.getTokenDetails.execute({
  context: { 
    tokenAddress: "${commonTokens.UNI}",
    includeHistorical: true,
    includePools: true 
  }
});
  `);
  
  console.log("\n3. Compare multiple tokens:");
  console.log(`
const comparison = await uniswapTokenTools.compareTokens.execute({
  context: { 
    tokenAddresses: ["${commonTokens.UNI}", "${commonTokens.WETH}"],
    includeHistorical: true 
  }
});
  `);
}

// Export for direct execution
export { testGetTopTokens, testGetTokenDetails, testCompareTokens, testErrorHandling };

/**
 * Run tests if this file is executed directly
 */
if (import.meta.main) {
  runAllTests()
    .then(() => {
      showUsageExamples();
      process.exit(0);
    })
    .catch((error) => {
      console.error("Test execution failed:", error);
      process.exit(1);
    });
}
