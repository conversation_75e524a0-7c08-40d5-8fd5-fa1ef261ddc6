/**
 * Uniswap Token Analysis Tools
 * 
 * A comprehensive suite of tools for analyzing Uniswap V3 tokens including:
 * - Top token rankings by volume, price changes, and liquidity
 * - Detailed token metrics and historical data
 * - Side-by-side token comparisons
 * - Real-time market data and insights
 */

import { getTopUniswapTokensTool } from "./get-top-tokens";
import { getTokenDetailsTool } from "./get-token-details";
import { compareTokensTool } from "./compare-tokens";

/**
 * Collection of all Uniswap token analysis tools
 */
export const uniswapTokenTools = {
  getTopUniswapTokens: getTopUniswapTokensTool,
  getTokenDetails: getTokenDetailsTool,
  compareTokens: compareTokensTool,
} as const;

/**
 * Tool descriptions for documentation and discovery
 */
export const toolDescriptions = {
  getTopUniswapTokens: "Retrieve and rank top Uniswap tokens by volume, price changes, and liquidity metrics",
  getTokenDetails: "Get comprehensive details for a specific token including metrics and pool information",
  compareTokens: "Compare multiple tokens side by side with detailed performance analysis",
} as const;

/**
 * Environment requirements
 */
export const environmentRequirements = {
  THE_GRAPH_API_KEY: "Optional - Get your API key from https://thegraph.com/ for higher rate limits",
} as const;

/**
 * Supported ranking criteria for token analysis
 */
export const rankingCriteria = [
  'volume24h',
  'volumeUSD', 
  'totalValueLockedUSD',
  'priceChange24h',
  'priceChange7d',
] as const;

/**
 * Supported timeframes for analysis
 */
export const timeframes = [
  '24h',
  '7d', 
  '30d',
] as const;

/**
 * Common token addresses for testing and examples
 */
export const commonTokens = {
  WETH: '******************************************',
  USDC: '******************************************',
  USDT: '******************************************',
  UNI: '******************************************',
  WBTC: '******************************************',
  DAI: '******************************************',
  LINK: '******************************************',
  AAVE: '******************************************',
  COMP: '******************************************',
  MKR: '******************************************',
} as const;

/**
 * Usage examples for each tool
 */
export const usageExamples = {
  getTopUniswapTokens: {
    basic: { 
      limit: 20, 
      sortBy: "volumeUSD", 
      timeframe: "24h" 
    },
    advanced: { 
      limit: 50, 
      sortBy: "priceChange24h", 
      timeframe: "7d", 
      includeHistorical: true,
      minVolume: 10000 
    },
  },
  getTokenDetails: {
    basic: { 
      tokenAddress: commonTokens.UNI 
    },
    comprehensive: { 
      tokenAddress: commonTokens.WETH, 
      includeHistorical: true, 
      includePools: true, 
      historicalDays: 30 
    },
  },
  compareTokens: {
    basic: { 
      tokenAddresses: [commonTokens.UNI, commonTokens.AAVE, commonTokens.COMP] 
    },
    detailed: { 
      tokenAddresses: [commonTokens.WETH, commonTokens.USDC, commonTokens.WBTC], 
      includeHistorical: true,
      comparisonMetrics: ["volume", "priceChange", "tvl"] 
    },
  },
} as const;

/**
 * Tool configuration and limits
 */
export const toolLimits = {
  maxTokensPerQuery: 100,
  maxTokensForComparison: 10,
  maxHistoricalDays: 365,
  cacheTimeoutMinutes: 5,
  rateLimitPerMinute: 60,
} as const;

/**
 * Error codes and messages
 */
export const errorCodes = {
  INVALID_TOKEN_ADDRESS: 'Invalid Ethereum token address format',
  TOKEN_NOT_FOUND: 'Token not found in Uniswap V3',
  API_RATE_LIMIT: 'API rate limit exceeded, please try again later',
  NETWORK_ERROR: 'Network error while fetching data',
  INVALID_PARAMETERS: 'Invalid parameters provided',
  CACHE_ERROR: 'Error accessing cache',
} as const;

/**
 * Helper function to validate tool inputs
 */
export function validateToolInputs(toolName: string, inputs: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  switch (toolName) {
    case 'getTopUniswapTokens':
      if (inputs.limit && (inputs.limit < 1 || inputs.limit > toolLimits.maxTokensPerQuery)) {
        errors.push(`Limit must be between 1 and ${toolLimits.maxTokensPerQuery}`);
      }
      break;
      
    case 'getTokenDetails':
      if (!inputs.tokenAddress || typeof inputs.tokenAddress !== 'string') {
        errors.push('Token address is required and must be a string');
      }
      if (inputs.historicalDays && (inputs.historicalDays < 1 || inputs.historicalDays > toolLimits.maxHistoricalDays)) {
        errors.push(`Historical days must be between 1 and ${toolLimits.maxHistoricalDays}`);
      }
      break;
      
    case 'compareTokens':
      if (!Array.isArray(inputs.tokenAddresses)) {
        errors.push('Token addresses must be an array');
      } else if (inputs.tokenAddresses.length < 2) {
        errors.push('At least 2 token addresses are required for comparison');
      } else if (inputs.tokenAddresses.length > toolLimits.maxTokensForComparison) {
        errors.push(`Maximum ${toolLimits.maxTokensForComparison} tokens can be compared at once`);
      }
      break;
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Export types for external use
 */
export type {
  TokenData,
  TokenMetrics,
  TokenRanking,
  TokenComparison,
  RankingCriteria,
  UniswapQueryParams,
} from './types';

/**
 * Export utility functions
 */
export {
  formatNumber,
  formatPercentageChange,
  calculatePriceChange,
  normalizeTokenAddress,
  isValidEthereumAddress,
} from './utils';

/**
 * Export client for advanced usage
 */
export { UniswapClient } from './uniswap-client';

/**
 * Default export for easy importing
 */
export default uniswapTokenTools;
