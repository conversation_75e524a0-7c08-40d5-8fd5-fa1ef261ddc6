/**
 * TypeScript interfaces for Uniswap V3 subgraph API responses and tool parameters
 */

export interface UniswapApiResponse<T = any> {
  data: T;
  errors?: Array<{
    message: string;
    locations?: Array<{
      line: number;
      column: number;
    }>;
    path?: string[];
  }>;
}

export interface TokenData {
  id: string; // Token contract address
  symbol: string;
  name: string;
  decimals: string;
  totalValueLocked: string;
  totalValueLockedUSD: string;
  volume: string;
  volumeUSD: string;
  feesUSD: string;
  poolCount: string;
  txCount: string;
  derivedETH: string;
  priceUSD?: string;
  // Price change data (calculated)
  priceChange1h?: number;
  priceChange24h?: number;
  priceChange7d?: number;
  // Volume data for different timeframes
  volume24h?: string;
  volume7d?: string;
  volume30d?: string;
}

export interface TokenDayData {
  id: string;
  date: number;
  token: {
    id: string;
    symbol: string;
    name: string;
  };
  volume: string;
  volumeUSD: string;
  open: string;
  high: string;
  low: string;
  close: string;
  priceUSD: string;
  totalValueLocked: string;
  totalValueLockedUSD: string;
}

export interface PoolData {
  id: string;
  token0: {
    id: string;
    symbol: string;
    name: string;
    decimals: string;
  };
  token1: {
    id: string;
    symbol: string;
    name: string;
    decimals: string;
  };
  feeTier: string;
  liquidity: string;
  sqrtPrice: string;
  tick: string;
  token0Price: string;
  token1Price: string;
  volumeUSD: string;
  totalValueLockedUSD: string;
  txCount: string;
}

export interface TokensResponse {
  tokens: TokenData[];
}

export interface TokenDayDataResponse {
  tokenDayDatas: TokenDayData[];
}

export interface PoolsResponse {
  pools: PoolData[];
}

export interface UniswapQueryParams {
  first?: number;
  skip?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  where?: Record<string, any>;
  block?: {
    number?: number;
    timestamp?: number;
  };
}

export interface RankingCriteria {
  primary: 'volume24h' | 'volumeUSD' | 'totalValueLockedUSD' | 'priceChange24h';
  secondary?: 'priceChange24h' | 'priceChange7d' | 'totalValueLockedUSD' | 'volume24h';
  tertiary?: 'totalValueLockedUSD' | 'poolCount' | 'txCount';
}

export interface TokenRanking {
  rank: number;
  token: TokenData;
  score: number;
  metrics: {
    volume24hUSD: number;
    priceChange24h: number;
    priceChange7d: number;
    totalValueLockedUSD: number;
    marketCapUSD?: number;
  };
}

export interface TokenComparison {
  tokens: TokenData[];
  comparison: {
    volume: {
      highest: TokenData;
      lowest: TokenData;
    };
    priceChange24h: {
      highest: TokenData;
      lowest: TokenData;
    };
    tvl: {
      highest: TokenData;
      lowest: TokenData;
    };
  };
  summary: string;
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

export interface TokenMetrics {
  tokenAddress: string;
  symbol: string;
  name: string;
  currentPrice: number;
  priceChanges: {
    '1h': number;
    '24h': number;
    '7d': number;
  };
  volume: {
    '24h': number;
    '7d': number;
    '30d': number;
  };
  liquidity: {
    totalValueLocked: number;
    poolCount: number;
  };
  marketData: {
    marketCap?: number;
    fullyDilutedValuation?: number;
    circulatingSupply?: number;
  };
  lastUpdated: string;
}

export interface AlertThresholds {
  volumeThreshold: number;
  priceChangeThreshold: number;
  liquidityThreshold: number;
}

export interface TokenAlert {
  isAlert: boolean;
  tokenAddress: string;
  symbol: string;
  alertType: 'volume_spike' | 'price_surge' | 'price_drop' | 'liquidity_change';
  currentValue: number;
  threshold: number;
  percentageChange: number;
  message: string;
  timestamp: string;
}
