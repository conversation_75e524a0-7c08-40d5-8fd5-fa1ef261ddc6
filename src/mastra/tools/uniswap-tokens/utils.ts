/**
 * Utility functions for Uniswap token analysis
 */

import type {
    CacheEntry,
    RankingCriteria,
    TokenData,
    TokenDayData,
    TokenRanking
} from './types';

/**
 * Calculate price change percentage between two values
 */
export function calculatePriceChange(currentPrice: number, previousPrice: number): number {
  if (previousPrice === 0) return 0;
  return ((currentPrice - previousPrice) / previousPrice) * 100;
}

/**
 * Get date range for historical data queries
 */
export function getDateRange(days: number): { from: number; to: number } {
  const to = Math.floor(Date.now() / 1000);
  const from = to - (days * 24 * 60 * 60);
  return { from, to };
}

/**
 * Calculate price changes from historical data
 */
export function calculatePriceChanges(
  currentPrice: number,
  historicalData: TokenDayData[]
): { priceChange1h?: number; priceChange24h?: number; priceChange7d?: number } {
  const sortedData = historicalData.sort((a, b) => b.date - a.date);

  const result: any = {};

  // 24h change (most recent vs 1 day ago)
  const oneDayAgo = sortedData.find(d => d.date <= (Date.now() / 1000) - (24 * 60 * 60));
  if (oneDayAgo) {
    result.priceChange24h = calculatePriceChange(currentPrice, parseFloat(oneDayAgo.close));
  }

  // 7d change (most recent vs 7 days ago)
  const sevenDaysAgo = sortedData.find(d => d.date <= (Date.now() / 1000) - (7 * 24 * 60 * 60));
  if (sevenDaysAgo) {
    result.priceChange7d = calculatePriceChange(currentPrice, parseFloat(sevenDaysAgo.close));
  }

  return result;
}

/**
 * Calculate volume for different timeframes
 */
export function calculateVolumeMetrics(historicalData: TokenDayData[]): {
  volume24h: string;
  volume7d: string;
  volume30d: string;
} {
  const sortedData = historicalData.sort((a, b) => b.date - a.date);
  const currentTime = Date.now() / 1000;

  // 24h volume (most recent day)
  const volume24h = sortedData[0]?.volumeUSD || '0';

  // 7d volume (sum of last 7 days)
  const volume7d = sortedData
    .filter(d => d.date >= currentTime - (7 * 24 * 60 * 60))
    .reduce((sum, d) => sum + parseFloat(d.volumeUSD || '0'), 0)
    .toString();

  // 30d volume (sum of last 30 days)
  const volume30d = sortedData
    .filter(d => d.date >= currentTime - (30 * 24 * 60 * 60))
    .reduce((sum, d) => sum + parseFloat(d.volumeUSD || '0'), 0)
    .toString();

  return { volume24h, volume7d, volume30d };
}

/**
 * Rank tokens based on specified criteria
 */
export function rankTokens(
  tokens: TokenData[],
  criteria: RankingCriteria
): TokenRanking[] {
  const scoredTokens = tokens.map(token => {
    let score = 0;

    // Primary criteria (weight: 50%)
    const primaryValue = getTokenMetricValue(token, criteria.primary);
    score += primaryValue * 0.5;

    // Secondary criteria (weight: 30%)
    if (criteria.secondary) {
      const secondaryValue = getTokenMetricValue(token, criteria.secondary);
      score += secondaryValue * 0.3;
    }

    // Tertiary criteria (weight: 20%)
    if (criteria.tertiary) {
      const tertiaryValue = getTokenMetricValue(token, criteria.tertiary);
      score += tertiaryValue * 0.2;
    }

    return {
      token,
      score,
      metrics: {
        volume24hUSD: parseFloat(token.volume24h || token.volumeUSD || '0'),
        priceChange24h: token.priceChange24h || 0,
        priceChange7d: token.priceChange7d || 0,
        totalValueLockedUSD: parseFloat(token.totalValueLockedUSD || '0'),
      },
    };
  });

  // Sort by score (descending) and assign ranks
  const rankedTokens = scoredTokens
    .sort((a, b) => b.score - a.score)
    .map((item, index) => ({
      ...item,
      rank: index + 1,
    }));

  return rankedTokens;
}

/**
 * Get numeric value for a token metric
 */
function getTokenMetricValue(token: TokenData, metric: string): number {
  switch (metric) {
    case 'volume24h':
      return parseFloat(token.volume24h || token.volumeUSD || '0');
    case 'volumeUSD':
      return parseFloat(token.volumeUSD || '0');
    case 'totalValueLockedUSD':
      return parseFloat(token.totalValueLockedUSD || '0');
    case 'priceChange24h':
      return Math.abs(token.priceChange24h || 0); // Use absolute value for ranking
    case 'priceChange7d':
      return Math.abs(token.priceChange7d || 0);
    case 'poolCount':
      return parseFloat(token.poolCount || '0');
    case 'txCount':
      return parseFloat(token.txCount || '0');
    default:
      return 0;
  }
}

/**
 * Format large numbers for display
 */
export function formatNumber(value: number, decimals: number = 2): string {
  if (value >= 1e9) {
    return `$${(value / 1e9).toFixed(decimals)}B`;
  } else if (value >= 1e6) {
    return `$${(value / 1e6).toFixed(decimals)}M`;
  } else if (value >= 1e3) {
    return `$${(value / 1e3).toFixed(decimals)}K`;
  } else {
    return `$${value.toFixed(decimals)}`;
  }
}

/**
 * Format percentage change with color indicators
 */
export function formatPercentageChange(change: number): string {
  const sign = change >= 0 ? '+' : '';
  const emoji = change >= 0 ? '🟢' : '🔴';
  return `${emoji} ${sign}${change.toFixed(2)}%`;
}

/**
 * Validate Ethereum address format
 */
export function isValidEthereumAddress(address: string): boolean {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
}

/**
 * Normalize token address to lowercase
 */
export function normalizeTokenAddress(address: string): string {
  if (!isValidEthereumAddress(address)) {
    throw new Error(`Invalid Ethereum address: ${address}`);
  }
  return address.toLowerCase();
}

/**
 * Create cache key for data caching
 */
export function createCacheKey(prefix: string, params: Record<string, any>): string {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|');
  return `${prefix}:${sortedParams}`;
}

/**
 * Check if cache entry is valid
 */
export function isCacheValid<T>(entry: CacheEntry<T> | null): boolean {
  if (!entry) return false;
  return Date.now() < entry.expiresAt;
}

/**
 * Create cache entry with expiration
 */
export function createCacheEntry<T>(data: T, ttlMinutes: number = 5): CacheEntry<T> {
  const now = Date.now();
  return {
    data,
    timestamp: now,
    expiresAt: now + (ttlMinutes * 60 * 1000),
  };
}

/**
 * Generate human-readable summary for token data
 */
export function generateTokenSummary(token: TokenData): string {
  const volume = formatNumber(parseFloat(token.volumeUSD || '0'));
  const tvl = formatNumber(parseFloat(token.totalValueLockedUSD || '0'));
  const priceChange24h = token.priceChange24h ? formatPercentageChange(token.priceChange24h) : 'N/A';

  return `${token.name} (${token.symbol}): ${volume} 24h volume, ${tvl} TVL, ${priceChange24h} 24h change`;
}

/**
 * Sort tokens by multiple criteria
 */
export function sortTokensMultiCriteria(
  tokens: TokenData[],
  criteria: Array<{ field: keyof TokenData; direction: 'asc' | 'desc' }>
): TokenData[] {
  return tokens.sort((a, b) => {
    for (const criterion of criteria) {
      const aValue = parseFloat(String(a[criterion.field]) || '0');
      const bValue = parseFloat(String(b[criterion.field]) || '0');

      if (aValue !== bValue) {
        return criterion.direction === 'desc' ? bValue - aValue : aValue - bValue;
      }
    }
    return 0;
  });
}
