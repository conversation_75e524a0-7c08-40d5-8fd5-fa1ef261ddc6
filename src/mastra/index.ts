import { <PERSON><PERSON> } from "@mastra/core";
import { <PERSON><PERSON><PERSON>og<PERSON> } from "@mastra/loggers";
import { UpstashStore } from "@mastra/upstash";
import { awsAgent } from "./agents/aws-agent";
import { cryptoSentimentAgent } from "./agents/crypto-sentiment-agent";
import { uniswapAgent } from "./agents/uniswap-agent";
import { weatherWorkflow } from "./workflows/weather-workflow";

const storage = new UpstashStore({
	url: process.env.UPSTASH_URL as string,
	token: process.env.UPSTASH_TOKEN as string,
});

export const mastra = new Mastra({
	workflows: { weatherWorkflow },
	agents: { awsAgent, cryptoSentimentAgent, uniswapAgent },
	storage,
	server: {
		cors: false,
	},
	logger: new PinoLogger({
		name: "<PERSON><PERSON>",
		level: "info",
	}),
});
